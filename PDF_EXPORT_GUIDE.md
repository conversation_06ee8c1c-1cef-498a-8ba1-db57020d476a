# 📄 Guide d'Export PDF - BTK Calculator

## 🎯 Fonctionnalité d'Export PDF

L'application BTK Calculator dispose maintenant d'une fonctionnalité avancée d'export PDF qui génère un **relevé de notes officiel** avec un tableau professionnel.

## ✨ Caractéristiques du PDF

### 📋 Format Officiel
- **En-tête institutionnel** : République Algérienne, Ministère de l'Enseignement Supérieur
- **Informations personnalisables** : Université, Faculté
- **Titre du document** : Relevé de Notes - Semestre X
- **Date d'édition** automatique

### 👤 Informations Étudiant
- **Nom et Prénom** : Saisie personnalisée
- **Matricule** : Numéro d'identification étudiant
- **Université** : Nom de l'établissement (modifiable)
- **Sauvegarde automatique** des informations pour les prochains exports

### 📊 Tableau des Notes
Le tableau PDF comprend les colonnes suivantes :
- **Module** : Nom complet du module
- **Coef** : Coefficient du module
- **Examen** : Note d'examen (/20)
- **TD** : Note de travaux dirigés (/20) ou N/A
- **TP** : Note de travaux pratiques (/20) ou N/A
- **Moyenne** : Moyenne du module
- **Pondérée** : Moyenne × Coefficient

### 🎨 Design Professionnel
- **Couleurs institutionnelles** : Bleu et vert pour l'en-tête
- **Tableau structuré** : Bordures, alternance de couleurs
- **Mise en forme** : Gras pour les totaux, couleurs pour les mentions
- **Responsive** : Adaptation automatique du contenu

## 🚀 Comment Utiliser

### 1. Accès à la Fonctionnalité
- **Navbar** : Cliquer sur l'icône 📄 dans la barre de navigation
- **Boutons d'action** : Utiliser le bouton "Export PDF" dans les contrôles

### 2. Saisie des Informations
1. Une modal s'ouvre automatiquement
2. Remplir les champs :
   - **Nom** : Votre nom de famille
   - **Prénom** : Votre prénom
   - **Matricule** : Votre numéro étudiant
   - **Université** : Nom de votre établissement
3. Cliquer sur "📄 Générer le PDF"

### 3. Génération du PDF
- Le PDF se génère automatiquement
- Téléchargement automatique du fichier
- Nom du fichier : `Releve_Notes_S[X]_[DATE].pdf`

## 📋 Contenu du PDF

### En-tête Officiel
```
RÉPUBLIQUE ALGÉRIENNE DÉMOCRATIQUE ET POPULAIRE
MINISTÈRE DE L'ENSEIGNEMENT SUPÉRIEUR ET DE LA RECHERCHE SCIENTIFIQUE
[NOM DE L'UNIVERSITÉ]

RELEVÉ DE NOTES - SEMESTRE [X]
```

### Informations Étudiant
```
Nom: [NOM]                    Prénom: [PRÉNOM]
Matricule: [MATRICULE]        Date d'édition: [DATE]
```

### Tableau des Résultats
Le tableau inclut tous les modules avec leurs notes et calculs automatiques.

### Résumé des Résultats
- **Moyenne Générale** : Calculée automatiquement
- **Coefficients Utilisés** : X / Total
- **Mention** : Très Bien, Bien, Assez Bien, Passable, ou Ajourné

### Barème de Notation
- **Très Bien** : ≥ 16/20
- **Bien** : 14-15.99/20
- **Assez Bien** : 12-13.99/20
- **Passable** : 10-11.99/20
- **Ajourné** : < 10/20

### Signature et Cachet
Espace réservé pour la signature et le cachet de l'établissement.

## 💾 Sauvegarde des Données

### Informations Persistantes
- Les informations étudiant sont **sauvegardées automatiquement**
- Réutilisation lors des prochains exports
- Stockage local sécurisé (localStorage)

### Modification des Informations
- Possibilité de modifier les informations à chaque export
- Mise à jour automatique des données sauvegardées

## 🔧 Fonctionnalités Techniques

### Bibliothèques Utilisées
- **jsPDF** : Génération de documents PDF
- **jsPDF-AutoTable** : Création de tableaux professionnels

### Calculs Automatiques
- **Moyennes pondérées** : Calcul automatique selon les coefficients
- **Moyenne générale** : Basée uniquement sur les modules avec notes
- **Mentions** : Attribution automatique selon le barème

### Gestion des Données Manquantes
- **Notes vides** : Affichées comme "--"
- **Modules sans TD/TP** : Marqués "N/A"
- **Calculs partiels** : Prise en compte des modules complétés

## 🎨 Personnalisation

### Couleurs du Document
- **Primaire** : #667eea (Bleu)
- **Secondaire** : #48bb78 (Vert)
- **Texte** : #2d3748 (Gris foncé)
- **Arrière-plan** : #f8f9fa (Gris clair)

### Mise en Page
- **Format** : A4 (210 × 297 mm)
- **Marges** : 20mm de chaque côté
- **Police** : Helvetica (standard PDF)
- **Tailles** : Variables selon les sections

## 🔍 Validation et Contrôles

### Vérifications Automatiques
- **Données cohérentes** : Validation des calculs
- **Format correct** : Respect des standards PDF
- **Informations complètes** : Gestion des champs vides

### Messages d'Erreur
- **Génération échouée** : Toast d'erreur avec détails
- **Données manquantes** : Avertissements appropriés
- **Problèmes techniques** : Messages informatifs

## 📱 Compatibilité

### Navigateurs Supportés
- ✅ **Chrome** 80+
- ✅ **Firefox** 75+
- ✅ **Safari** 13+
- ✅ **Edge** 80+

### Appareils
- ✅ **Desktop** : Fonctionnalité complète
- ✅ **Tablette** : Interface adaptée
- ✅ **Mobile** : Génération possible

## 🚨 Limitations

### Taille du Document
- **Maximum** : 50 modules par semestre
- **Performance** : Optimisée pour les relevés standards

### Personnalisation
- **Template fixe** : Format institutionnel standard
- **Couleurs** : Palette prédéfinie
- **Logo** : Non supporté actuellement

## 🔮 Améliorations Futures

### Fonctionnalités Prévues
- **Templates multiples** : Différents formats d'établissements
- **Logo personnalisé** : Upload d'image d'établissement
- **Export par email** : Envoi direct du PDF
- **Impression directe** : Interface d'impression intégrée

### Optimisations
- **Performance** : Génération plus rapide
- **Qualité** : Résolution améliorée
- **Accessibilité** : Support des lecteurs d'écran

---

**Version** : 2.0.0  

**Développé avec** ❤️ pour les étudiants de Master 1 TIC
