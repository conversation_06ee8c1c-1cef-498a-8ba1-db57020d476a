# 🎓 BTK Calculateur de Moyenne - Master 1 TIC

## 📋 Description

Application web moderne et professionnelle pour calculer les moyennes des étudiants de Master 1 TIC. L'application supporte les deux semestres avec une interface intuitive et de nombreuses fonctionnalités avancées.

## ✨ Fonctionnalités Principales

### 🎯 Calcul de Moyennes
- **Calcul en temps réel** : Les moyennes se mettent à jour automatiquement lors de la saisie
- **Support des deux semestres** : Basculement facile entre S1 et S2
- **Gestion des coefficients** : Prise en compte automatique des coefficients de chaque module
- **Notes TD/TP** : Support des notes de travaux dirigés et pratiques selon les modules

### 🎨 Interface Moderne
- **Design responsive** : Optimisé pour desktop, tablette et mobile
- **Thème sombre/clair** : Basculement entre les modes d'affichage
- **Deux vues disponibles** :
  - 📋 Vue tableau (style relevé de notes officiel)
  - 🗃️ Vue cartes (interface moderne et intuitive)

### 📊 Suivi et Statistiques
- **Barre de progression** : Visualisation du pourcentage de modules complétés
- **Statistiques en temps réel** : Nombre de modules, coefficients totaux, progression
- **Badges de progression** : Indicateurs visuels sur chaque semestre

### 🔍 Validation et Contrôles
- **Validation en temps réel** : Vérification automatique des saisies
- **Messages d'erreur contextuels** : Aide à la correction des erreurs
- **Filtres intelligents** : Affichage des modules complétés/à compléter
- **Vérification globale** : Bouton pour valider toutes les notes

### 💾 Sauvegarde et Export
- **Sauvegarde automatique** : Les données sont sauvegardées toutes les 30 secondes
- **Sauvegarde manuelle** : Bouton pour sauvegarder à tout moment
- **Export JSON** : Exportation des données pour archivage
- **Persistance des données** : Les notes sont conservées entre les sessions

### 🔔 Notifications
- **Toast notifications** : Messages informatifs non-intrusifs
- **Feedback visuel** : Confirmations des actions utilisateur
- **Alertes contextuelles** : Avertissements et erreurs

## 🏗️ Structure du Projet

```
Master-1-TIC-S1/
├── index.html                 # Page principale
├── mon-projet/
│   ├── script.js             # Logique JavaScript
│   ├── style.css             # Styles de base
│   └── advanced-styles.css   # Styles avancés et thèmes
└── README.md                 # Documentation
```

## 🚀 Installation et Utilisation

### Prérequis
- Navigateur web moderne (Chrome, Firefox, Safari, Edge)
- Serveur web local (optionnel)

### Lancement
1. **Méthode simple** : Ouvrir `index.html` directement dans le navigateur
2. **Avec serveur local** :
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Node.js (avec http-server)
   npx http-server
   
   # PHP
   php -S localhost:8000
   ```

### Utilisation
1. **Sélectionner le semestre** : Cliquer sur "Semestre 1" ou "Semestre 2"
2. **Saisir les notes** : Entrer les notes dans les champs correspondants
3. **Visualiser les résultats** : La moyenne se calcule automatiquement
4. **Changer de vue** : Utiliser les boutons 📋/🗃️ pour basculer entre tableau et cartes
5. **Sauvegarder** : Utiliser le bouton 💾 pour sauvegarder manuellement

## 📚 Modules par Semestre

### Semestre 1
- Chaines logistiques et TIC (Coef: 2)
- Anglais (Coef: 1)
- Techniques de fouille de données (Coef: 3)
- Technologies des réseaux d'entreprise (Coef: 2)
- Base de données avancées (Coef: 2)
- E-commerce et marketing digital (Coef: 2)
- Droit d'éthique et de déontologie (Coef: 2)
- Entreprenariat (Coef: 1)
- Gouvernance des systèmes d'information (Coef: 2)

### Semestre 2
- Modelisation et Simulation avansee (Coef: 2)
- Anglais (Coef: 1)
- Text et web mining (Coef: 3)
- AID (Coef: 2)
- syberSecurite (Coef: 2)
- Sociologie de l'internet (Coef: 2)
- informatique legale et tic (Coef: 2)
- Partenariat (Coef: 1)
- Genie Logiciel (Coef: 2)

## 🎯 Système de Notation

| Mention | Plage de notes |
|---------|----------------|
| Très Bien | ≥ 16/20 |
| Bien | 14-15.99/20 |
| Assez Bien | 12-13.99/20 |
| Passable | 10-11.99/20 |
| Ajourné | < 10/20 |

## 🛠️ Technologies Utilisées

- **HTML5** : Structure sémantique
- **CSS3** : Styles modernes avec variables CSS et animations
- **JavaScript ES6+** : Logique applicative moderne
- **Google Fonts** : Typographies Inter et JetBrains Mono
- **LocalStorage API** : Persistance des données
- **Responsive Design** : Adaptation multi-écrans

## 🎨 Fonctionnalités Avancées

### Thème Sombre
- Basculement automatique des couleurs
- Sauvegarde de la préférence utilisateur
- Adaptation de tous les composants

### Auto-sauvegarde
- Sauvegarde automatique toutes les 30 secondes
- Détection des modifications
- Récupération automatique au rechargement

### Validation Intelligente
- Validation en temps réel des saisies
- Limitation automatique des valeurs (0-20)
- Messages d'aide contextuels
- Formatage automatique des décimales

### Export de Données
- Format JSON structuré
- Horodatage des exports
- Données complètes avec métadonnées

## 🔧 Personnalisation

### Variables CSS
Les couleurs et styles peuvent être personnalisés via les variables CSS dans `:root` :

```css
:root {
  --primary: #667eea;
  --accent: #48bb78;
  --text: #2d3748;
  /* ... autres variables */
}
```

### Configuration JavaScript
Les paramètres peuvent être modifiés dans `script.js` :

```javascript
let autoSaveEnabled = true;  // Auto-sauvegarde
let currentSemester = 2;     // Semestre par défaut
let isDarkTheme = false;     // Thème par défaut
```

## 📱 Compatibilité

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+
- ✅ Mobile (iOS Safari, Chrome Mobile)

## 🤝 Contribution

Pour contribuer au projet :
1. Fork le repository
2. Créer une branche feature
3. Commiter les changements
4. Pousser vers la branche
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

## 👨‍💻 Auteur

Développé avec ❤️ pour les étudiants de Master 1 TIC

---


