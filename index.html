<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculateur de moyenne - Master 1 TIC</title>
    <link rel="stylesheet" href="./mon-projet/style.css">
    <link rel="stylesheet" href="./mon-projet/advanced-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400&display=swap" rel="stylesheet">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎓</text></svg>">

    <!-- Bibliothèques pour l'export PDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
</head>
<body>
    <!-- Barre de navigation moderne -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- Logo et marque -->
            <div class="nav-brand">
                <div class="nav-logo">
                    <span class="nav-icon">🎓</span>
                    <div class="logo-animation"></div>
                </div>
                <div class="nav-brand-text">
                    <span class="nav-title">BTK Calculator</span>
                    <span class="nav-subtitle">Master 1 TIC</span>
                </div>
            </div>

            <!-- Menu de navigation central -->
            <div class="nav-menu">
                <div class="nav-links">
                    <a href="#" class="nav-link active" data-section="calculator">
                        <span class="nav-link-icon">📊</span>
                        <span class="nav-link-text">Calculateur</span>
                    </a>
                    <a href="#" class="nav-link" data-section="statistics">
                        <span class="nav-link-icon">📈</span>
                        <span class="nav-link-text">Statistiques</span>
                    </a>
                    <a href="#" class="nav-link" data-section="history">
                        <span class="nav-link-icon">📚</span>
                        <span class="nav-link-text">Historique</span>
                    </a>
                </div>
            </div>

            <!-- Actions et outils -->
            <div class="nav-actions">
                <div class="nav-tools">
                    <button class="nav-btn nav-btn-icon" onclick="saveData()" title="Sauvegarder">
                        <span class="btn-icon">💾</span>
                        <span class="btn-tooltip">Sauvegarder</span>
                    </button>
                    <button class="nav-btn nav-btn-icon" onclick="exportToPDF()" title="Exporter en PDF">
                        <span class="btn-icon">📄</span>
                        <span class="btn-tooltip">Export PDF</span>
                    </button>
                    <button class="nav-btn nav-btn-icon" onclick="showHelp()" title="Aide">
                        <span class="btn-icon">❓</span>
                        <span class="btn-tooltip">Aide</span>
                    </button>
                </div>

                <div class="nav-divider"></div>

                <div class="nav-settings">
                    <button class="nav-btn nav-btn-theme" onclick="toggleTheme()" title="Changer le thème">
                        <span class="theme-icon" id="theme-icon">🌙</span>
                        <span class="theme-text">Mode sombre</span>
                    </button>
                </div>

                <!-- Menu mobile -->
                <button class="nav-mobile-toggle" onclick="toggleMobileMenu()">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
            </div>
        </div>

        <!-- Menu mobile -->
        <div class="nav-mobile-menu" id="navMobileMenu">
            <div class="nav-mobile-links">
                <a href="#" class="nav-mobile-link active">
                    <span class="nav-link-icon">📊</span>
                    <span>Calculateur</span>
                </a>
                <a href="#" class="nav-mobile-link">
                    <span class="nav-link-icon">📈</span>
                    <span>Statistiques</span>
                </a>
                <a href="#" class="nav-mobile-link">
                    <span class="nav-link-icon">📚</span>
                    <span>Historique</span>
                </a>
            </div>
            <div class="nav-mobile-actions">
                <button class="nav-mobile-btn" onclick="saveData()">
                    <span>💾</span> Sauvegarder
                </button>
                <button class="nav-mobile-btn" onclick="exportToPDF()">
                    <span>📄</span> Export PDF
                </button>
                <button class="nav-mobile-btn" onclick="showHelp()">
                    <span>❓</span> Aide
                </button>
                <button class="nav-mobile-btn" onclick="toggleTheme()">
                    <span id="mobile-theme-icon">🌙</span> Mode sombre
                </button>
            </div>
        </div>
    </nav>

    <!-- En-tête officiel -->
    <header class="official-header">
        <div class="republic-title">
            République Algérienne Démocratique et Populaire
        </div>
        <div class="ministry-title">
            Ministère de l'Enseignement Supérieur et de la Recherche Scientifique
        </div>
        <div class="university-info">
            Université - Faculté de Mathematique et informatique<br>
            Département d'Informatique
        </div>
        <div class="document-title">
            Calculateur de Moyenne Officiel<br>
            Master 1 - Technologies de l'Information et de la Communication
        </div>
    </header>

    <div class="container">
        <!-- En-tête avec statistiques -->
        <header class="app-header">
            <h1>Relevé de Notes - Semestre 2</h1>
            <div class="stats-bar" id="statsBar">
                <div class="stat-item">
                    <span class="stat-label">Modules</span>
                    <span class="stat-value" id="totalModules">9</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Coefficients</span>
                    <span class="stat-value" id="totalCoef">17</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Progression</span>
                    <span class="stat-value" id="progressPercent">0%</span>
                </div>
            </div>
        </header>

        <!-- Sélecteur de semestre amélioré -->
        <div class="semester-selector">
            <h3>Choisir le semestre :</h3>
            <div class="semester-buttons">
                <button class="semester-btn" data-semester="1" onclick="changeSemester(1)">
                    <span class="semester-icon">📚</span>
                    <span class="semester-text">Semestre 1</span>
                    <span class="semester-badge" id="s1-badge">0/9</span>
                </button>
                <button class="semester-btn active" data-semester="2" onclick="changeSemester(2)">
                    <span class="semester-icon">📖</span>
                    <span class="semester-text">Semestre 2</span>
                    <span class="semester-badge" id="s2-badge">0/9</span>
                </button>
            </div>
        </div>

        <!-- Barre de progression -->
        <div class="progress-container">
            <div class="progress-label">
                <span>Progression de saisie</span>
                <span id="progressText">0/9 modules</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- Filtres et options -->
        <div class="controls-panel">
            <div class="filter-controls">
                <button class="filter-btn active" onclick="filterModules('all')" data-filter="all">
                    Tous les modules
                </button>
                <button class="filter-btn" onclick="filterModules('completed')" data-filter="completed">
                    Complétés
                </button>
                <button class="filter-btn" onclick="filterModules('incomplete')" data-filter="incomplete">
                    À compléter
                </button>
            </div>
            <div class="view-controls">
                <button class="view-btn active" onclick="changeView('table')" data-view="table" title="Vue tableau">
                    📋
                </button>
                <button class="view-btn" onclick="changeView('cards')" data-view="cards" title="Vue cartes">
                    🗃️
                </button>
            </div>
        </div>

        <div class="modules-grid" id="modulesContainer"></div>

        <!-- Contrôles de validation améliorés -->
        <div class="validation-controls">
            <button type="button" onclick="validateAllInputs()" class="validate-btn">
                <span class="btn-icon">🔍</span>
                <span class="btn-text">Vérifier toutes les notes</span>
            </button>
            <button type="button" onclick="clearAllInputs()" class="clear-btn">
                <span class="btn-icon">🗑️</span>
                <span class="btn-text">Effacer tout</span>
            </button>
            <button type="button" onclick="saveData()" class="save-btn">
                <span class="btn-icon">💾</span>
                <span class="btn-text">Sauvegarder</span>
            </button>
            <button type="button" onclick="loadData()" class="load-btn">
                <span class="btn-icon">📂</span>
                <span class="btn-text">Charger</span>
            </button>
            <button type="button" onclick="exportToPDF()" class="pdf-btn">
                <span class="btn-icon">📄</span>
                <span class="btn-text">Export PDF</span>
            </button>
        </div>

        <!-- Résultat amélioré -->
        <div class="result-container">
            <div id="result" class="result"></div>
            <div id="result-details" class="result-details"></div>
        </div>

    </div>

    <!-- Modal d'aide -->
    <div class="modal" id="helpModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Guide d'utilisation</h2>
                <button class="modal-close" onclick="closeModal('helpModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="help-section">
                    <h3>🎯 Comment utiliser le calculateur</h3>
                    <ul>
                        <li>Sélectionnez votre semestre (1 ou 2)</li>
                        <li>Saisissez vos notes dans les champs correspondants</li>
                        <li>La moyenne se calcule automatiquement en temps réel</li>
                        <li>Utilisez les boutons de validation pour vérifier vos saisies</li>
                    </ul>
                </div>
                <div class="help-section">
                    <h3>📊 Système de notation</h3>
                    <ul>
                        <li><strong>Très Bien:</strong> ≥ 16/20</li>
                        <li><strong>Bien:</strong> 14-15.99/20</li>
                        <li><strong>Assez Bien:</strong> 12-13.99/20</li>
                        <li><strong>Passable:</strong> 10-11.99/20</li>
                        <li><strong>Ajourné:</strong> < 10/20</li>
                    </ul>
                </div>
                <div class="help-section">
                    <h3>💾 Fonctionnalités</h3>
                    <ul>
                        <li>Sauvegarde automatique des données</li>
                        <li>Export des résultats en PDF</li>
                        <li>Mode sombre/clair</li>
                        <li>Interface responsive</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>



    <!-- Modal pour les informations étudiant -->
    <div class="modal" id="studentInfoModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Informations Étudiant</h2>
                <button class="modal-close" onclick="closeModal('studentInfoModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="studentInfoForm">
                    <div class="form-group">
                        <label for="studentName">Nom :</label>
                        <input type="text" id="studentName" placeholder="Entrez votre nom">
                    </div>
                    <div class="form-group">
                        <label for="studentFirstName">Prénom :</label>
                        <input type="text" id="studentFirstName" placeholder="Entrez votre prénom">
                    </div>
                    <div class="form-group">
                        <label for="studentId">Matricule :</label>
                        <input type="text" id="studentId" placeholder="Entrez votre matricule">
                    </div>
                    <div class="form-group">
                        <label for="university">Université :</label>
                        <input type="text" id="university" placeholder="Nom de l'université" value="UNIVERSITÉ - FACULTÉ">
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="generatePDFWithInfo()" class="pdf-generate-btn">
                            📄 Générer le PDF
                        </button>
                        <button type="button" onclick="closeModal('studentInfoModal')" class="cancel-btn">
                            Annuler
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>



    <!-- Toast notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="./mon-projet/script.js"></script>
</body>
</html>