// Modules par semestre
const modulesBySemester = {
    1: [
        { name: "Chaines logistiques et TIC", coef: 2, td: true, tp: false },
        { name: "<PERSON><PERSON><PERSON>", coef: 1, td: false, tp: false },
        { name: "Techniques de fouille de données", coef: 3, td: true, tp: true },
        { name: "Technologies des réseaux d'entreprise", coef: 2, td: false, tp: true },
        { name: "Base de données avancées", coef: 2, td: false, tp: true },
        { name: "E-commerce et marketing digital", coef: 2, td: false, tp: true },
        { name: "Droit d'éthique et de déontologie", coef: 2, td: false, tp: false },
        { name: "Entreprenariat", coef: 1, td: false, tp: false },
        { name: "Gouvernance des systèmes d'information", coef: 2, td: true, tp: false }
    ],
    2: [
        { name: "Modelisation et Simulation avansee", coef: 2, td: false, tp: true },
        { name: "<PERSON><PERSON><PERSON>", coef: 1, td: false, tp: false },
        { name: "Text et web mining", coef: 3, td: true, tp: true },
        { name: "AID", coef: 2, td: false, tp: true },
        { name: "syberSecurite", coef: 2, td: true, tp: false },
        { name: "Sociologie de l'internet", coef: 2, td: false, tp: true },
        { name: "informatique legale et tic", coef: 2, td: true, tp: false },
        { name: "Partenariat", coef: 1, td: false, tp: false },
        { name: "Genie Logiciel", coef: 2, td: true, tp: false }
    ]
};

// Variables globales
let currentSemester = 2;
let modules = modulesBySemester[currentSemester];
let currentView = 'table';
let currentFilter = 'all';
let isDarkTheme = false;
let autoSaveEnabled = true;

// Données de progression
let progressData = {
    1: { completed: 0, total: 9 },
    2: { completed: 0, total: 9 }
};



// Fonction pour changer de semestre
function changeSemester(semester) {
    currentSemester = semester;
    modules = modulesBySemester[currentSemester];

    // Vider le conteneur
    const container = document.getElementById('modulesContainer');
    container.innerHTML = '';

    // Vider le résultat
    document.getElementById('result').innerHTML = '';

    // Supprimer les résumés de validation existants
    const summary = document.getElementById('validation-summary');
    if (summary) {
        summary.remove();
    }

    // Recréer la vue selon le mode actuel
    if (currentView === 'cards') {
        createCardsView();
    } else {
        createModuleInputs();
    }

    // Mettre à jour le titre
    updateTitle();

    // Mettre à jour les boutons de semestre
    updateSemesterButtons();

    // Afficher les informations du nouveau semestre
    displaySemesterInfo();

    // Animation de transition
    container.style.opacity = '0';
    setTimeout(() => {
        container.style.opacity = '1';
    }, 100);
}

function updateTitle() {
    const title = document.querySelector('h1');
    title.textContent = `BTK-Calculateur de moyenne - Master 1 TIC S${currentSemester}`;
}

function updateSemesterButtons() {
    const buttons = document.querySelectorAll('.semester-btn');
    buttons.forEach(btn => {
        const semester = parseInt(btn.dataset.semester);
        if (semester === currentSemester) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });
}

function createModuleInputs() {
    const container = document.getElementById('modulesContainer');
    // S'assurer que le conteneur a la bonne classe pour la vue tableau
    container.className = 'modules-grid';

    // Créer la table
    const table = document.createElement('table');
    table.className = 'notes-table';

    // En-tête du tableau
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th>Module</th>
            <th>Coef</th>
            <th>Examen (/20)</th>
            <th>TD (/20)</th>
            <th>TP (/20)</th>
            <th>Moyenne Module</th>
            <th>Moyenne × Coef</th>
        </tr>
    `;
    table.appendChild(thead);

    // Corps du tableau
    const tbody = document.createElement('tbody');

    modules.forEach((module, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="module-name">${module.name}</td>
            <td class="coef">${module.coef}</td>
            <td class="grade-input">
                <input type="number" min="0" max="20" step="0.01" id="exam-${index}" placeholder="--">
            </td>
            <td class="grade-input">
                ${module.td ? `<input type="number" min="0" max="20" step="0.01" id="td-${index}" placeholder="--">` : '<span class="not-applicable">--</span>'}
            </td>
            <td class="grade-input">
                ${module.tp ? `<input type="number" min="0" max="20" step="0.01" id="tp-${index}" placeholder="--">` : '<span class="not-applicable">--</span>'}
            </td>
            <td class="module-avg" id="module-avg-${index}">--</td>
            <td class="weighted-avg" id="module-average-${index}">0.00</td>
        `;

        // Ajout des écouteurs d'événements avec validation
        const inputs = row.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('input', (e) => {
                validateInput(e.target);
                updateModuleAverage(index);
            });
            input.addEventListener('blur', (e) => {
                formatInput(e.target);
            });
            input.addEventListener('keypress', (e) => {
                validateKeyPress(e);
            });
        });

        tbody.appendChild(row);
    });

    table.appendChild(tbody);

    // Ajouter une ligne de total
    const tfoot = document.createElement('tfoot');
    tfoot.innerHTML = `
        <tr class="total-row">
            <td colspan="5" class="total-label">MOYENNE GÉNÉRALE DU SEMESTRE</td>
            <td id="general-average" class="general-average">--</td>
            <td id="total-weighted" class="total-weighted">--</td>
        </tr>
        <tr class="coef-total-row">
            <td colspan="5" class="total-label">COEFFICIENTS UTILISÉS / TOTAL</td>
            <td id="total-coef" class="total-coef">0 / ${modules.reduce((sum, module) => sum + module.coef, 0)}</td>
            <td class="mention" id="mention">--</td>
        </tr>
    `;
    table.appendChild(tfoot);

    container.appendChild(table);
}

// Fonctions de validation
function validateKeyPress(e) {
    const char = String.fromCharCode(e.which);
    const value = e.target.value;

    // Autoriser les chiffres, le point décimal, et les touches de contrôle
    if (!/[0-9.]/.test(char) && e.which !== 8 && e.which !== 46 && e.which !== 37 && e.which !== 39) {
        e.preventDefault();
        showValidationMessage(e.target, "Seuls les chiffres et le point décimal sont autorisés");
        return false;
    }

    // Empêcher plus d'un point décimal
    if (char === '.' && value.includes('.')) {
        e.preventDefault();
        showValidationMessage(e.target, "Un seul point décimal est autorisé");
        return false;
    }

    // Limiter à 2 chiffres avant le point et 2 après
    if (char !== '.' && !isControlKey(e.which)) {
        const parts = value.split('.');
        if (parts[0].length >= 2 && value.indexOf('.') === -1) {
            e.preventDefault();
            showValidationMessage(e.target, "Maximum 2 chiffres avant le point décimal");
            return false;
        }
        if (parts.length > 1 && parts[1].length >= 2) {
            e.preventDefault();
            showValidationMessage(e.target, "Maximum 2 chiffres après le point décimal");
            return false;
        }
    }
}

function isControlKey(keyCode) {
    return keyCode === 8 || keyCode === 46 || keyCode === 37 || keyCode === 39 || keyCode === 9;
}

function validateInput(input) {
    const value = parseFloat(input.value);

    // Supprimer les messages d'erreur précédents
    clearValidationMessage(input);

    if (input.value === '') {
        input.classList.remove('error', 'warning');
        return true;
    }

    if (isNaN(value)) {
        input.classList.add('error');
        showValidationMessage(input, "Veuillez entrer un nombre valide");
        return false;
    }

    if (value < 0) {
        input.classList.add('error');
        input.value = '';
        showValidationMessage(input, "La note ne peut pas être négative");
        return false;
    }

    if (value > 20) {
        input.classList.add('error');
        input.value = '20';
        showValidationMessage(input, "La note ne peut pas dépasser 20");
        return false;
    }

    // Avertissement pour les notes très basses
    if (value < 5) {
        input.classList.add('warning');
        input.classList.remove('error');
        showValidationMessage(input, "Note très basse - Vérifiez la saisie", 'warning');
    } else {
        input.classList.remove('error', 'warning');
    }

    return true;
}

function formatInput(input) {
    if (input.value !== '' && !isNaN(parseFloat(input.value))) {
        const value = parseFloat(input.value);
        input.value = value.toFixed(2);
    }
}

function showValidationMessage(input, message, type = 'error') {
    clearValidationMessage(input);

    const messageDiv = document.createElement('div');
    messageDiv.className = `validation-message ${type}`;
    messageDiv.textContent = message;
    messageDiv.id = `msg-${input.id}`;

    input.parentNode.appendChild(messageDiv);

    // Supprimer le message après 3 secondes
    setTimeout(() => {
        clearValidationMessage(input);
    }, 3000);
}

function clearValidationMessage(input) {
    const existingMessage = document.getElementById(`msg-${input.id}`);
    if (existingMessage) {
        existingMessage.remove();
    }
}

// Validation globale du formulaire
function validateAllInputs() {
    const inputs = document.querySelectorAll('.notes-table input[type="number"]');
    let hasErrors = false;
    let errorCount = 0;
    let warningCount = 0;

    inputs.forEach(input => {
        if (!validateInput(input)) {
            hasErrors = true;
            if (input.classList.contains('error')) {
                errorCount++;
            } else if (input.classList.contains('warning')) {
                warningCount++;
            }
        }
    });

    // Afficher un résumé des erreurs si nécessaire
    if (errorCount > 0 || warningCount > 0) {
        showFormValidationSummary(errorCount, warningCount);
    }

    return !hasErrors;
}

function showFormValidationSummary(errorCount, warningCount) {
    // Supprimer le résumé précédent s'il existe
    const existingSummary = document.getElementById('validation-summary');
    if (existingSummary) {
        existingSummary.remove();
    }

    if (errorCount === 0 && warningCount === 0) return;

    const summary = document.createElement('div');
    summary.id = 'validation-summary';
    summary.className = 'validation-summary';

    let message = '';
    if (errorCount > 0) {
        message += `⚠️ ${errorCount} erreur(s) détectée(s). `;
    }
    if (warningCount > 0) {
        message += `⚡ ${warningCount} avertissement(s). `;
    }
    message += 'Veuillez vérifier vos saisies.';

    summary.innerHTML = `
        <div class="summary-content">
            <span class="summary-icon">🔍</span>
            <span class="summary-text">${message}</span>
            <button class="summary-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    const container = document.querySelector('.container');
    const table = document.querySelector('.notes-table');
    container.insertBefore(summary, table);

    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (summary.parentNode) {
            summary.remove();
        }
    }, 5000);
}

function updateModuleAverage(index) {
    const module = modules[index];
    const examInput = document.getElementById(`exam-${index}`);
    const exam = parseFloat(examInput.value) || 0;
    let activityNote = 0;
    let hasActivity = module.td || module.tp;
    let moduleAvg = 0;

    if (hasActivity) {
        if (module.td && module.tp) {
            const td = parseFloat(document.getElementById(`td-${index}`).value) || 0;
            const tp = parseFloat(document.getElementById(`tp-${index}`).value) || 0;
            activityNote = (td + tp) / 2;
        } else if (module.td) {
            activityNote = parseFloat(document.getElementById(`td-${index}`).value) || 0;
        } else if (module.tp) {
            activityNote = parseFloat(document.getElementById(`tp-${index}`).value) || 0;
        }
        moduleAvg = (exam + activityNote) / 2;
    } else {
        moduleAvg = exam;
    }

    const weightedAverage = moduleAvg * module.coef;

    // Afficher la moyenne du module
    const moduleAvgCell = document.getElementById(`module-avg-${index}`);
    moduleAvgCell.textContent = examInput.value ? moduleAvg.toFixed(2) : '--';

    // Appliquer les classes CSS selon la moyenne du module
    if (examInput.value) {
        let gradeClass = '';
        if (moduleAvg >= 18) gradeClass = 'excellent';
        else if (moduleAvg >= 16) gradeClass = 'tres-bien';
        else if (moduleAvg >= 14) gradeClass = 'bien';
        else if (moduleAvg >= 12) gradeClass = 'assez-bien';
        else if (moduleAvg >= 10) gradeClass = 'passable';
        else gradeClass = 'insuffisant';

        moduleAvgCell.className = `module-average ${gradeClass}`;
    } else {
        moduleAvgCell.className = 'module-average non-note';
    }

    // Afficher la moyenne pondérée
    document.getElementById(`module-average-${index}`).textContent = examInput.value ? weightedAverage.toFixed(2) : '0.00';

    // Mettre à jour la moyenne générale en temps réel
    updateGeneralAverage();

    // Mettre à jour la progression
    updateProgress();
}

function updateGeneralAverage() {
    let totalWeighted = 0;
    let totalCoef = 0;
    let hasValidGrades = false;

    modules.forEach((module, index) => {
        const examInput = document.getElementById(`exam-${index}`);
        // Vérifier si le module a au moins une note d'examen
        if (examInput && examInput.value && examInput.value.trim() !== '') {
            const moduleAvgElement = document.getElementById(`module-avg-${index}`);
            const moduleAvg = parseFloat(moduleAvgElement.textContent) || 0;

            // Calculer la contribution pondérée de ce module
            const weightedContribution = moduleAvg * module.coef;
            totalWeighted += weightedContribution;
            totalCoef += module.coef;
            hasValidGrades = true;
        }
    });

    // Calculer la moyenne générale seulement avec les modules qui ont des notes
    const generalAverage = hasValidGrades ? totalWeighted / totalCoef : 0;

    // Mettre à jour l'affichage
    const generalAverageElement = document.getElementById('general-average');
    generalAverageElement.textContent = hasValidGrades ? generalAverage.toFixed(2) : '--';

    document.getElementById('total-weighted').textContent = hasValidGrades ? totalWeighted.toFixed(2) : '--';

    // Mettre à jour le total des coefficients utilisés
    const totalPossibleCoef = modules.reduce((sum, module) => sum + module.coef, 0);
    document.getElementById('total-coef').textContent = `${totalCoef} / ${totalPossibleCoef}`;

    // Déterminer la mention et appliquer les classes CSS
    let mention = '--';
    let gradeClass = '';

    if (hasValidGrades) {
        if (generalAverage >= 18) {
            mention = 'Excellent';
            gradeClass = 'excellent';
        } else if (generalAverage >= 16) {
            mention = 'Très Bien';
            gradeClass = 'tres-bien';
        } else if (generalAverage >= 14) {
            mention = 'Bien';
            gradeClass = 'bien';
        } else if (generalAverage >= 12) {
            mention = 'Assez Bien';
            gradeClass = 'assez-bien';
        } else if (generalAverage >= 10) {
            mention = 'Passable';
            gradeClass = 'passable';
        } else {
            mention = 'Ajourné';
            gradeClass = 'insuffisant';
        }
    }

    // Appliquer les classes CSS à la moyenne générale
    if (hasValidGrades) {
        generalAverageElement.className = `general-average-value ${gradeClass}`;
        // Appliquer la classe à la cellule parent si elle existe
        const generalAverageCell = generalAverageElement.closest('td');
        if (generalAverageCell) {
            generalAverageCell.className = `general-average-cell ${gradeClass}`;
        }
    } else {
        generalAverageElement.className = 'general-average-value';
        const generalAverageCell = generalAverageElement.closest('td');
        if (generalAverageCell) {
            generalAverageCell.className = 'general-average-cell';
        }
    }

    // Mettre à jour la mention avec les classes CSS
    const mentionElement = document.getElementById('mention');
    mentionElement.textContent = mention;

    if (hasValidGrades) {
        mentionElement.className = `mention-cell ${gradeClass}`;
    } else {
        mentionElement.className = 'mention-cell';
    }

    // Afficher le message selon la moyenne (sans alerte)
    if (hasValidGrades) {
        updateResultMessage(generalAverage);
    } else {
        // Effacer le message si aucune note n'est saisie
        document.getElementById('result').innerHTML = '';
    }
}

function updateResultMessage(finalAverage) {
    const resultDiv = document.getElementById('result');

    // Compter les modules avec des notes
    const modulesWithGrades = modules.filter((_, index) => {
        const examInput = document.getElementById(`exam-${index}`);
        return examInput && examInput.value && examInput.value.trim() !== '';
    }).length;

    const totalModules = modules.length;
    const progressText = `(${modulesWithGrades}/${totalModules} modules)`;

    // Déterminer la mention et les messages
    let mention, mentionClass, mainMessage, emoji;

    if (finalAverage >= 16) {
        mention = 'Très Bien';
        mentionClass = 'mention-tres-bien';
        mainMessage = '🏆 EXCELLENT TRAVAIL! 🏆';
        emoji = '🌟';
    } else if (finalAverage >= 14) {
        mention = 'Bien';
        mentionClass = 'mention-bien';
        mainMessage = '👏 TRÈS BON RÉSULTAT! 👏';
        emoji = '💎';
    } else if (finalAverage >= 12) {
        mention = 'Assez Bien';
        mentionClass = 'mention-assez-bien';
        mainMessage = '👍 BON TRAVAIL! 👍';
        emoji = '⭐';
    } else if (finalAverage >= 10) {
        mention = 'Passable';
        mentionClass = 'mention-passable';
        mainMessage = '✅ VALIDÉ! ✅';
        emoji = '📈';
    } else {
        mention = 'Ajourné';
        mentionClass = 'mention-ajourne';
        mainMessage = '💪 COURAGE POUR LA SUITE! 💪';
        emoji = '🎯';
    }

    resultDiv.innerHTML = `
        <div class="result-main">${mainMessage}</div>
        <div class="result-score">${emoji} ${finalAverage.toFixed(2)}/20 ${emoji}</div>
        <div class="result-progress">${progressText}</div>
    `;

    // Appliquer les classes CSS appropriées
    const baseClass = finalAverage >= 10 ? 'result-success' : 'result-warning';
    resultDiv.className = `result ${baseClass} ${mentionClass}`;

    // Supprimer les styles inline pour laisser le CSS prendre le contrôle
    resultDiv.style.color = '';
}


// Fonction conservée pour compatibilité mais plus nécessaire
function calculateAverage() {
    // La moyenne est maintenant calculée en temps réel
    updateGeneralAverage();
}

// Fonction pour effacer tous les inputs
function clearAllInputs() {
    if (confirm('Êtes-vous sûr de vouloir effacer toutes les notes saisies ?')) {
        const inputs = document.querySelectorAll('.notes-table input[type="number"]');
        inputs.forEach(input => {
            input.value = '';
            input.classList.remove('error', 'warning');
            clearValidationMessage(input);
        });

        // Réinitialiser l'affichage
        modules.forEach((_, index) => {
            document.getElementById(`module-avg-${index}`).textContent = '--';
            document.getElementById(`module-average-${index}`).textContent = '0.00';
        });

        document.getElementById('general-average').textContent = '--';
        document.getElementById('total-weighted').textContent = '--';
        document.getElementById('mention').textContent = '--';
        document.getElementById('result').innerHTML = '';

        // Supprimer le résumé de validation s'il existe
        const summary = document.getElementById('validation-summary');
        if (summary) {
            summary.remove();
        }
    }
}

// Fonction pour afficher les informations du semestre
function displaySemesterInfo() {
    const totalModules = modules.length;
    const totalCoef = modules.reduce((sum, module) => sum + module.coef, 0);
    const modulesWithTD = modules.filter(m => m.td).length;
    const modulesWithTP = modules.filter(m => m.tp).length;

    console.log(`📊 Semestre ${currentSemester} - Informations:`);
    console.log(`   • ${totalModules} modules au total`);
    console.log(`   • ${totalCoef} coefficients au total`);
    console.log(`   • ${modulesWithTD} modules avec TD`);
    console.log(`   • ${modulesWithTP} modules avec TP`);
}

// Fonction d'initialisation complète
function initializeApp() {
    // Créer les inputs des modules
    createModuleInputs();

    // Mettre à jour le titre
    updateTitle();

    // Mettre à jour les boutons de semestre
    updateSemesterButtons();

    // Afficher les informations du semestre
    displaySemesterInfo();

    // Ajouter un message de bienvenue
    console.log('🎓 Calculateur de moyenne Master 1 TIC initialisé avec succès!');
    console.log('💡 Astuce: Utilisez les boutons pour changer de semestre');
}

// ===== NOUVELLES FONCTIONNALITÉS AVANCÉES =====

// Gestion du thème
function toggleTheme() {
    isDarkTheme = !isDarkTheme;
    document.body.classList.toggle('dark-theme', isDarkTheme);
    updateThemeIcons();
    localStorage.setItem('darkTheme', isDarkTheme);
    showToast(isDarkTheme ? 'Mode sombre activé' : 'Mode clair activé', 'info');
}

// Système de notifications toast
function showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <span class="toast-icon">${getToastIcon(type)}</span>
            <span class="toast-message">${message}</span>
            <button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    document.getElementById('toastContainer').appendChild(toast);

    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => toast.remove(), 300);
    }, duration);
}

function getToastIcon(type) {
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
    };
    return icons[type] || 'ℹ️';
}

// Gestion des modales
function showHelp() {
    document.getElementById('helpModal').style.display = 'flex';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Fermer modal en cliquant à l'extérieur
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// Fonction pour sauvegarder les données actuelles avant changement de vue
function saveCurrentViewData() {
    const currentData = {};
    modules.forEach((_, index) => {
        const examInput = document.getElementById(`exam-${index}`);
        const tdInput = document.getElementById(`td-${index}`);
        const tpInput = document.getElementById(`tp-${index}`);

        currentData[index] = {
            exam: examInput ? examInput.value : '',
            td: tdInput ? tdInput.value : '',
            tp: tpInput ? tpInput.value : ''
        };
    });
    return currentData;
}

// Fonction pour restaurer les données dans la nouvelle vue
function restoreViewData(data) {
    modules.forEach((_, index) => {
        const gradeData = data[index];
        if (gradeData) {
            const examInput = document.getElementById(`exam-${index}`);
            const tdInput = document.getElementById(`td-${index}`);
            const tpInput = document.getElementById(`tp-${index}`);

            if (examInput && gradeData.exam) {
                examInput.value = gradeData.exam;
                validateInput(examInput);
            }
            if (tdInput && gradeData.td) {
                tdInput.value = gradeData.td;
                validateInput(tdInput);
            }
            if (tpInput && gradeData.tp) {
                tpInput.value = gradeData.tp;
                validateInput(tpInput);
            }

            // Mettre à jour les moyennes
            updateModuleAverage(index);
        }
    });
}

// Gestion des vues
function changeView(view) {
    // Sauvegarder les données actuelles
    const currentData = saveCurrentViewData();

    currentView = view;
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.view === view);
    });

    // Vider complètement le conteneur et réinitialiser sa classe
    const container = document.getElementById('modulesContainer');
    container.innerHTML = '';
    container.className = 'modules-grid'; // Réinitialiser la classe par défaut

    if (view === 'cards') {
        createCardsView();
    } else {
        createModuleInputs();
    }

    // Restaurer les données dans la nouvelle vue
    setTimeout(() => {
        restoreViewData(currentData);
    }, 100);

    showToast(`Vue ${view === 'cards' ? 'cartes' : 'tableau'} activée`, 'info');
}

// Vue en cartes
function createCardsView() {
    const container = document.getElementById('modulesContainer');
    // Le conteneur est déjà vidé dans changeView()
    container.className = 'modules-cards';

    modules.forEach((module, index) => {
        const card = document.createElement('div');
        card.className = 'module-card-view';
        card.innerHTML = `
            <div class="card-header">
                <h4 class="card-title">${module.name}</h4>
                <span class="card-coef">Coef: ${module.coef}</span>
            </div>
            <div class="card-body">
                <div class="grade-row">
                    <label>Examen (/20)</label>
                    <input type="number" min="0" max="20" step="0.01" id="exam-${index}" placeholder="--">
                </div>
                ${module.td ? `
                <div class="grade-row">
                    <label>TD (/20)</label>
                    <input type="number" min="0" max="20" step="0.01" id="td-${index}" placeholder="--">
                </div>` : ''}
                ${module.tp ? `
                <div class="grade-row">
                    <label>TP (/20)</label>
                    <input type="number" min="0" max="20" step="0.01" id="tp-${index}" placeholder="--">
                </div>` : ''}
            </div>
            <div class="card-footer">
                <div class="card-average">
                    <span>Moyenne: </span>
                    <span id="module-avg-${index}" class="avg-value">--</span>
                </div>
                <div class="card-weighted">
                    <span>Pondérée: </span>
                    <span id="module-average-${index}" class="weighted-value">0.00</span>
                </div>
            </div>
        `;

        // Ajouter les événements
        const inputs = card.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('input', (e) => {
                validateInput(e.target);
                updateModuleAverage(index);
            });
            input.addEventListener('blur', (e) => {
                formatInput(e.target);
            });
            input.addEventListener('keypress', (e) => {
                validateKeyPress(e);
            });
        });

        container.appendChild(card);
    });
}

// Filtrage des modules
function filterModules(filter) {
    currentFilter = filter;
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.filter === filter);
    });

    const rows = document.querySelectorAll('.notes-table tbody tr, .module-card-view');
    rows.forEach((row, index) => {
        const examInput = document.getElementById(`exam-${index}`);
        const hasValue = examInput && examInput.value !== '';

        let show = true;
        if (filter === 'completed' && !hasValue) show = false;
        if (filter === 'incomplete' && hasValue) show = false;

        row.style.display = show ? '' : 'none';
    });

    showToast(`Filtre "${filter}" appliqué`, 'info');
}

// Mise à jour de la progression
function updateProgress() {
    const completedModules = modules.filter((_, index) => {
        const examInput = document.getElementById(`exam-${index}`);
        return examInput && examInput.value !== '';
    }).length;

    progressData[currentSemester].completed = completedModules;
    const percentage = Math.round((completedModules / modules.length) * 100);

    // Mettre à jour l'affichage
    document.getElementById('progressPercent').textContent = `${percentage}%`;
    document.getElementById('progressText').textContent = `${completedModules}/${modules.length} modules`;
    document.getElementById('progressFill').style.width = `${percentage}%`;

    // Mettre à jour les badges des semestres
    document.getElementById('s1-badge').textContent = `${progressData[1].completed}/${progressData[1].total}`;
    document.getElementById('s2-badge').textContent = `${progressData[2].completed}/${progressData[2].total}`;

    // Mettre à jour les statistiques
    updateStats();
}

function updateStats() {
    document.getElementById('totalModules').textContent = modules.length;
    document.getElementById('totalCoef').textContent = modules.reduce((sum, m) => sum + m.coef, 0);
}

// Sauvegarde et chargement des données
function saveData() {
    const data = {
        semester: currentSemester,
        grades: {},
        timestamp: new Date().toISOString()
    };

    modules.forEach((_, index) => {
        const examInput = document.getElementById(`exam-${index}`);
        const tdInput = document.getElementById(`td-${index}`);
        const tpInput = document.getElementById(`tp-${index}`);

        data.grades[index] = {
            exam: examInput ? examInput.value : '',
            td: tdInput ? tdInput.value : '',
            tp: tpInput ? tpInput.value : ''
        };
    });

    localStorage.setItem(`btk_calculator_s${currentSemester}`, JSON.stringify(data));
    showToast('Données sauvegardées avec succès', 'success');
}

function loadData() {
    const savedData = localStorage.getItem(`btk_calculator_s${currentSemester}`);
    if (!savedData) {
        showToast('Aucune sauvegarde trouvée', 'warning');
        return;
    }

    try {
        const data = JSON.parse(savedData);

        modules.forEach((_, index) => {
            const gradeData = data.grades[index];
            if (gradeData) {
                const examInput = document.getElementById(`exam-${index}`);
                const tdInput = document.getElementById(`td-${index}`);
                const tpInput = document.getElementById(`tp-${index}`);

                if (examInput && gradeData.exam) examInput.value = gradeData.exam;
                if (tdInput && gradeData.td) tdInput.value = gradeData.td;
                if (tpInput && gradeData.tp) tpInput.value = gradeData.tp;

                updateModuleAverage(index);
            }
        });

        showToast('Données chargées avec succès', 'success');
    } catch (error) {
        showToast('Erreur lors du chargement', 'error');
    }
}

// Auto-sauvegarde
function autoSave() {
    if (autoSaveEnabled) {
        saveData();
    }
}

// Export des données JSON
function exportData() {
    const data = {
        semester: currentSemester,
        modules: modules.map((module, index) => {
            const examInput = document.getElementById(`exam-${index}`);
            const tdInput = document.getElementById(`td-${index}`);
            const tpInput = document.getElementById(`tp-${index}`);
            const avgElement = document.getElementById(`module-avg-${index}`);

            return {
                name: module.name,
                coef: module.coef,
                exam: examInput ? examInput.value : '',
                td: tdInput ? tdInput.value : '',
                tp: tpInput ? tpInput.value : '',
                average: avgElement ? avgElement.textContent : '--'
            };
        }),
        generalAverage: document.getElementById('general-average').textContent,
        mention: document.getElementById('mention').textContent,
        exportDate: new Date().toLocaleString('fr-FR')
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `notes_s${currentSemester}_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);

    showToast('Données JSON exportées avec succès', 'success');
}

// Export en PDF avec tableau professionnel
function exportToPDF() {
    // Charger les informations sauvegardées
    loadStudentInfo();
    // Ouvrir la modal pour les informations étudiant
    document.getElementById('studentInfoModal').style.display = 'flex';
}

function loadStudentInfo() {
    const savedInfo = localStorage.getItem('studentInfo');
    if (savedInfo) {
        const info = JSON.parse(savedInfo);
        document.getElementById('studentName').value = info.name || '';
        document.getElementById('studentFirstName').value = info.firstName || '';
        document.getElementById('studentId').value = info.id || '';
        document.getElementById('university').value = info.university || 'UNIVERSITÉ - FACULTÉ';
    }
}

function saveStudentInfo() {
    const info = {
        name: document.getElementById('studentName').value,
        firstName: document.getElementById('studentFirstName').value,
        id: document.getElementById('studentId').value,
        university: document.getElementById('university').value
    };
    localStorage.setItem('studentInfo', JSON.stringify(info));
}

function generatePDFWithInfo() {
    // Sauvegarder les informations
    saveStudentInfo();

    // Fermer la modal
    closeModal('studentInfoModal');

    // Générer le PDF
    generatePDF();
}

function generatePDF() {
    try {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Configuration des couleurs
        const primaryColor = [30, 58, 138]; // #1e3a8a (bleu professionnel)
        const textColor = [31, 41, 55]; // #1f2937 (gris foncé)
        const lightGray = [248, 249, 250]; // #f8f9fa

        // En-tête officiel
        doc.setFontSize(16);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(...textColor);
        doc.text('RÉPUBLIQUE ALGÉRIENNE DÉMOCRATIQUE ET POPULAIRE', 105, 20, { align: 'center' });

        doc.setFontSize(14);
        doc.text('MINISTÈRE DE L\'ENSEIGNEMENT SUPÉRIEUR ET DE LA RECHERCHE SCIENTIFIQUE', 105, 30, { align: 'center' });

        doc.setFontSize(12);
        doc.setFont('helvetica', 'normal');
        const universityName = JSON.parse(localStorage.getItem('studentInfo') || '{}').university || 'UNIVERSITÉ - FACULTÉ';
        doc.text(universityName, 105, 40, { align: 'center' });

        // Ligne de séparation
        doc.setDrawColor(...primaryColor);
        doc.setLineWidth(1);
        doc.line(20, 45, 190, 45);

        // Titre du relevé
        doc.setFontSize(18);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(...primaryColor);
        doc.text(`RELEVÉ DE NOTES - SEMESTRE ${currentSemester}`, 105, 60, { align: 'center' });

        // Informations étudiant
        const studentInfo = JSON.parse(localStorage.getItem('studentInfo') || '{}');
        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');
        doc.setTextColor(...textColor);
        doc.text(`Nom: ${studentInfo.name || '___________________'}`, 20, 75);
        doc.text(`Prénom: ${studentInfo.firstName || '___________________'}`, 105, 75);
        doc.text(`Matricule: ${studentInfo.id || '___________________'}`, 20, 85);
        doc.text(`Date d'édition: ${new Date().toLocaleDateString('fr-FR')}`, 105, 85);

        // Préparer les données du tableau
        const tableData = [];
        let totalWeighted = 0;
        let totalCoef = 0;
        let modulesWithGrades = 0;

        modules.forEach((module, index) => {
            const examInput = document.getElementById(`exam-${index}`);
            const tdInput = document.getElementById(`td-${index}`);
            const tpInput = document.getElementById(`tp-${index}`);
            const avgElement = document.getElementById(`module-avg-${index}`);

            const examNote = examInput ? examInput.value || '--' : '--';
            const tdNote = module.td ? (tdInput ? tdInput.value || '--' : '--') : 'N/A';
            const tpNote = module.tp ? (tpInput ? tpInput.value || '--' : '--') : 'N/A';
            const moduleAvg = avgElement ? avgElement.textContent : '--';

            // Calculer la moyenne pondérée si des notes existent
            let weightedAvg = '--';
            if (examInput && examInput.value) {
                const avg = parseFloat(moduleAvg) || 0;
                weightedAvg = (avg * module.coef).toFixed(2);
                totalWeighted += parseFloat(weightedAvg);
                totalCoef += module.coef;
                modulesWithGrades++;
            }

            tableData.push([
                module.name,
                module.coef.toString(),
                examNote,
                tdNote,
                tpNote,
                moduleAvg,
                weightedAvg
            ]);
        });

        // Calculer la moyenne générale
        const generalAverage = totalCoef > 0 ? (totalWeighted / totalCoef).toFixed(2) : '--';

        // Déterminer la mention
        let mention = '--';
        if (generalAverage !== '--') {
            const avg = parseFloat(generalAverage);
            if (avg >= 16) mention = 'Très Bien';
            else if (avg >= 14) mention = 'Bien';
            else if (avg >= 12) mention = 'Assez Bien';
            else if (avg >= 10) mention = 'Passable';
            else mention = 'Ajourné';
        }

        // Ajouter les lignes de total
        tableData.push([
            { content: 'MOYENNE GÉNÉRALE DU SEMESTRE', colSpan: 5, styles: { fontStyle: 'bold', fillColor: lightGray } },
            { content: generalAverage, styles: { fontStyle: 'bold', fillColor: [212, 237, 218] } },
            { content: totalWeighted.toFixed(2), styles: { fontStyle: 'bold', fillColor: lightGray } }
        ]);

        tableData.push([
            { content: `COEFFICIENTS UTILISÉS / TOTAL`, colSpan: 5, styles: { fontStyle: 'bold', fillColor: lightGray } },
            { content: `${totalCoef} / ${modules.reduce((sum, m) => sum + m.coef, 0)}`, styles: { fontStyle: 'bold', fillColor: [255, 243, 205] } },
            { content: mention, styles: { fontStyle: 'bold', fillColor: [209, 236, 241] } }
        ]);

        // Créer le tableau avec autoTable
        doc.autoTable({
            head: [['Module', 'Coef', 'Examen', 'TD', 'TP', 'Moyenne', 'Pondérée']],
            body: tableData,
            startY: 95,
            theme: 'grid',
            headStyles: {
                fillColor: primaryColor,
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                fontSize: 10,
                halign: 'center'
            },
            bodyStyles: {
                fontSize: 9,
                textColor: textColor,
                halign: 'center'
            },
            columnStyles: {
                0: { halign: 'left', cellWidth: 60 }, // Module
                1: { halign: 'center', cellWidth: 15 }, // Coef
                2: { halign: 'center', cellWidth: 20 }, // Examen
                3: { halign: 'center', cellWidth: 15 }, // TD
                4: { halign: 'center', cellWidth: 15 }, // TP
                5: { halign: 'center', cellWidth: 20 }, // Moyenne
                6: { halign: 'center', cellWidth: 25 }  // Pondérée
            },
            alternateRowStyles: {
                fillColor: [250, 250, 250]
            },
            margin: { left: 20, right: 20 }
        });

        // Ajouter des informations supplémentaires en bas
        const finalY = doc.lastAutoTable.finalY + 20;

        doc.setFontSize(10);
        doc.setFont('helvetica', 'bold');
        doc.text('BARÈME DE NOTATION:', 20, finalY);

        doc.setFont('helvetica', 'normal');
        doc.text('• Très Bien: ≥ 16/20', 20, finalY + 10);
        doc.text('• Bien: 14-15.99/20', 20, finalY + 20);
        doc.text('• Assez Bien: 12-13.99/20', 20, finalY + 30);
        doc.text('• Passable: 10-11.99/20', 105, finalY + 10);
        doc.text('• Ajourné: < 10/20', 105, finalY + 20);

        // Signature et cachet
        doc.text('Signature et cachet de l\'établissement:', 105, finalY + 40);
        doc.rect(105, finalY + 45, 60, 30); // Rectangle pour signature

        // Pied de page
        doc.setFontSize(8);
        doc.setTextColor(100, 100, 100);
        doc.text(`Généré par BTK Calculator - ${new Date().toLocaleString('fr-FR')}`, 105, 280, { align: 'center' });

        // Sauvegarder le PDF
        const fileName = `Releve_Notes_S${currentSemester}_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);

        showToast('Relevé de notes PDF généré avec succès!', 'success');

    } catch (error) {
        console.error('Erreur lors de la génération du PDF:', error);
        showToast('Erreur lors de la génération du PDF', 'error');
    }
}











// ===== FONCTIONS DE NAVIGATION =====

// Basculer le menu mobile
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('navMobileMenu');
    const toggleBtn = document.querySelector('.nav-mobile-toggle');

    if (mobileMenu && toggleBtn) {
        mobileMenu.classList.toggle('active');
        toggleBtn.classList.toggle('active');
    }
}

// Fermer le menu mobile
function closeMobileMenu() {
    const mobileMenu = document.getElementById('navMobileMenu');
    const toggleBtn = document.querySelector('.nav-mobile-toggle');

    if (mobileMenu && toggleBtn) {
        mobileMenu.classList.remove('active');
        toggleBtn.classList.remove('active');
    }
}

// Gérer les clics sur les liens de navigation
function handleNavigation(section) {
    // Fermer le menu mobile
    closeMobileMenu();

    // Mettre à jour les liens actifs
    document.querySelectorAll('.nav-link, .nav-mobile-link').forEach(link => {
        link.classList.remove('active');
    });

    document.querySelectorAll(`[data-section="${section}"]`).forEach(link => {
        link.classList.add('active');
    });

    // Logique de navigation selon la section
    switch(section) {
        case 'calculator':
            // Afficher la section calculateur (par défaut)
            showToast('Section Calculateur', 'info');
            break;
        case 'statistics':
            // Afficher les statistiques
            showToast('Section Statistiques', 'info');
            break;
        case 'history':
            // Afficher l'historique
            showToast('Section Historique', 'info');
            break;
    }
}

// Mettre à jour l'icône du thème dans la navbar
function updateThemeIcons() {
    const isDark = document.body.classList.contains('dark-theme');
    const themeIcon = document.getElementById('theme-icon');
    const mobileThemeIcon = document.getElementById('mobile-theme-icon');
    const themeText = document.querySelector('.theme-text');

    if (themeIcon) {
        themeIcon.textContent = isDark ? '☀️' : '🌙';
    }

    if (mobileThemeIcon) {
        mobileThemeIcon.textContent = isDark ? '☀️' : '🌙';
    }

    if (themeText) {
        themeText.textContent = isDark ? 'Mode clair' : 'Mode sombre';
    }
}

// Initialisation complète
function initializeApp() {
    // Charger le thème sauvegardé
    const savedTheme = localStorage.getItem('darkTheme');
    if (savedTheme === 'true') {
        toggleTheme();
    }

    // Initialiser les événements de navigation
    initializeNavigation();

    // Créer les inputs des modules
    createModuleInputs();

    // Mettre à jour le titre
    updateTitle();

    // Mettre à jour les boutons de semestre
    updateSemesterButtons();

    // Mettre à jour les statistiques
    updateStats();
    updateProgress();

    // Afficher les informations du semestre
    displaySemesterInfo();

    // Charger les données sauvegardées
    loadData();

    // Configurer l'auto-sauvegarde
    setInterval(() => {
        if (autoSaveEnabled) {
            const hasData = modules.some((_, index) => {
                const examInput = document.getElementById(`exam-${index}`);
                return examInput && examInput.value !== '';
            });
            if (hasData) {
                autoSave();
            }
        }
    }, 30000); // Auto-save toutes les 30 secondes

    // Ajouter un message de bienvenue
    console.log('🎓 Calculateur de moyenne Master 1 TIC initialisé avec succès!');
    console.log('💡 Fonctionnalités: Thème sombre, Export, Auto-sauvegarde, Vue cartes, Navigation moderne');

    showToast('Application initialisée avec succès!', 'success');
}

// Initialiser les événements de navigation
function initializeNavigation() {
    // Événements pour les liens de navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const section = link.getAttribute('data-section');
            handleNavigation(section);
        });
    });

    // Événements pour les liens de navigation mobile
    document.querySelectorAll('.nav-mobile-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const section = link.getAttribute('data-section');
            handleNavigation(section);
        });
    });

    // Fermer le menu mobile en cliquant à l'extérieur
    document.addEventListener('click', (e) => {
        const mobileMenu = document.getElementById('navMobileMenu');
        const toggleBtn = document.querySelector('.nav-mobile-toggle');

        if (mobileMenu && mobileMenu.classList.contains('active')) {
            if (!mobileMenu.contains(e.target) && !toggleBtn.contains(e.target)) {
                closeMobileMenu();
            }
        }
    });

    // Fermer le menu mobile lors du redimensionnement
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
            closeMobileMenu();
        }
    });
}

// Fonction pour générer le PDF avec les informations étudiant
function generatePDFWithInfo() {
    // Récupérer les informations du formulaire
    const studentName = document.getElementById('studentName')?.value || '';
    const studentFirstName = document.getElementById('studentFirstName')?.value || '';
    const studentId = document.getElementById('studentId')?.value || '';
    const university = document.getElementById('university')?.value || 'UNIVERSITÉ - FACULTÉ';

    // Fermer la modal
    closeModal('studentInfoModal');

    // Générer le PDF avec les informations
    exportToPDF();

    showToast('PDF généré avec les informations étudiant', 'success');
}

// Initialisation
initializeApp();