:root {
    /* Palette de couleurs professionnelle et officielle */
    --primary: #1e3a8a;           /* Bleu marine officiel */
    --primary-light: #3b82f6;     /* Bleu clair */
    --primary-dark: #1e40af;      /* Bleu foncé */
    --secondary: #64748b;         /* Gris ardoise */
    --accent: #059669;            /* Vert <PERSON> */
    --accent-light: #10b981;      /* Vert clair */

    /* Couleurs de texte professionnelles */
    --text-primary: #1f2937;      /* Gris très foncé */
    --text-secondary: #374151;    /* Gris foncé */
    --text-muted: #6b7280;        /* <PERSON><PERSON> moyen */
    --text-light: #9ca3af;        /* Gris clair */
    --white: #ffffff;

    /* Arrière-plans officiels */
    --bg-primary: #ffffff;        /* Blanc pur */
    --bg-secondary: #f8fafc;      /* <PERSON><PERSON> très clair */
    --bg-tertiary: #f1f5f9;       /* Gris clair */
    --bg-accent: #eff6ff;         /* Bleu très clair */
    --bg-success: #ecfdf5;        /* Vert très clair */
    --bg-warning: #fffbeb;        /* Jaune très clair */
    --bg-error: #fef2f2;          /* Rouge très clair */

    /* Bordures professionnelles */
    --border-primary: #d1d5db;    /* Gris bordure */
    --border-secondary: #e5e7eb;  /* Gris bordure clair */
    --border-accent: #3b82f6;     /* Bleu bordure */

    /* Ombres subtiles et élégantes */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Rayons de bordure cohérents */
    --radius-sm: 4px;
    --radius: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;

    /* Transitions fluides */
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease-out;
    --transition-slow: all 0.3s ease-in-out;

    /* États de couleur */
    --success: #059669;
    --warning: #d97706;
    --error: #dc2626;
    --info: #2563eb;

    /* Typographie officielle */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
    --font-family-serif: 'Crimson Text', 'Times New Roman', serif;

    /* Espacements cohérents */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
}

/* Thème sombre moderne et élégant */
.dark-theme {
    /* Arrière-plans sophistiqués avec nuances de bleu-gris */
    --bg-primary: #0f172a;        /* Bleu-gris très foncé (slate-900) */
    --bg-secondary: #1e293b;      /* Bleu-gris foncé (slate-800) */
    --bg-tertiary: #334155;       /* Bleu-gris moyen (slate-700) */
    --bg-accent: #1e40af;         /* Bleu professionnel lumineux */
    --bg-success: #065f46;        /* Vert émeraude foncé */
    --bg-warning: #92400e;        /* Orange ambré foncé */
    --bg-error: #991b1b;          /* Rouge profond */

    /* Textes avec contraste optimal */
    --text-primary: #f8fafc;      /* Blanc pur légèrement teinté */
    --text-secondary: #e2e8f0;    /* Gris très clair avec nuance bleue */
    --text-muted: #94a3b8;        /* Gris moyen avec nuance bleue */
    --text-light: #64748b;        /* Gris foncé avec nuance bleue */

    /* Bordures harmonieuses */
    --border-primary: #475569;    /* Gris-bleu pour bordures principales */
    --border-secondary: #334155;  /* Gris-bleu plus foncé pour bordures secondaires */
    --border-accent: #3b82f6;     /* Bleu lumineux pour accents */

    /* Couleurs principales adaptées */
    --primary: #3b82f6;           /* Bleu plus lumineux pour le mode sombre */
    --primary-light: #60a5fa;     /* Bleu clair lumineux */
    --primary-dark: #1d4ed8;      /* Bleu foncé saturé */
    --accent: #10b981;            /* Vert émeraude lumineux */
    --accent-light: #34d399;      /* Vert clair lumineux */

    /* États de couleur optimisés */
    --success: #10b981;           /* Vert émeraude lumineux */
    --warning: #f59e0b;           /* Orange ambré lumineux */
    --error: #ef4444;             /* Rouge lumineux */
    --info: #3b82f6;              /* Bleu info lumineux */

    /* Blanc adapté */
    --white: #f8fafc;

    /* Ombres adaptées au mode sombre */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* ===== AMÉLIORATIONS SPÉCIFIQUES MODE SOMBRE ===== */
.dark-theme .navbar {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(71, 85, 105, 0.3);
}

.dark-theme .navbar::before {
    background: linear-gradient(90deg, #3b82f6, #10b981, #3b82f6);
    opacity: 0.8;
}

.dark-theme .nav-logo {
    background: linear-gradient(135deg, #3b82f6, #10b981);
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.dark-theme .nav-logo:hover {
    box-shadow: 0 8px 30px rgba(59, 130, 246, 0.4);
}

.dark-theme .nav-links {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.3);
    backdrop-filter: blur(10px);
}

.dark-theme .nav-link.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.dark-theme .nav-link:hover {
    background: rgba(30, 64, 175, 0.2);
    color: #60a5fa;
}

.dark-theme .nav-btn:hover {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.dark-theme .official-header {
    background: linear-gradient(135deg, #0f172a, #1e293b);
    border-bottom: 3px solid #3b82f6;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dark-theme .document-title {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.2), rgba(15, 23, 42, 0.8));
    border: 2px solid #3b82f6;
    color: #60a5fa;
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.dark-theme .container {
    background: rgba(15, 23, 42, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.2);
    backdrop-filter: blur(10px);
}

/* ===== TABLEAU AMÉLIORÉ MODE SOMBRE ===== */
.dark-theme .notes-table {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
    border: 2px solid rgba(59, 130, 246, 0.4);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.dark-theme .notes-table thead {
    background: linear-gradient(135deg, #1e40af, #3b82f6, #1d4ed8);
    position: relative;
    overflow: hidden;
}

.dark-theme .notes-table thead::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.dark-theme .notes-table th {
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.dark-theme .notes-table th:hover {
    background: rgba(255, 255, 255, 0.1);
}

.dark-theme .notes-table tbody tr {
    background: rgba(30, 41, 59, 0.4);
    border-bottom: 1px solid rgba(71, 85, 105, 0.3);
    transition: all 0.3s ease;
}

.dark-theme .notes-table tbody tr:nth-child(even) {
    background: rgba(51, 65, 85, 0.3);
}

.dark-theme .notes-table tbody tr:hover {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.2), rgba(59, 130, 246, 0.1));
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
    transform: translateY(-1px);
    border-color: rgba(59, 130, 246, 0.5);
}

.dark-theme .notes-table td {
    color: #e2e8f0;
    border-right: 1px solid rgba(71, 85, 105, 0.2);
    position: relative;
}

.dark-theme .btn {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
    border: 2px solid transparent;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.dark-theme .btn:hover {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    transform: translateY(-2px);
}

.dark-theme .semester-btn {
    background: rgba(30, 41, 59, 0.8);
    border: 2px solid #475569;
    color: #e2e8f0;
}

.dark-theme .semester-btn:hover {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
    border-color: #3b82f6;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.dark-theme .semester-btn.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-color: #60a5fa;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
}

.dark-theme .stat-item {
    background: rgba(30, 64, 175, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    backdrop-filter: blur(10px);
}

.dark-theme .stat-value {
    color: #60a5fa;
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

/* ===== SECTION RÉSULTATS PREMIUM MODE SOMBRE ===== */
.dark-theme .result-container {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
    border: 2px solid rgba(59, 130, 246, 0.4);
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    margin: var(--space-8) auto;
    max-width: 700px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.dark-theme .result-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #10b981, #3b82f6);
    opacity: 0.8;
}

.dark-theme .result-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.05), transparent);
    animation: shimmerResult 4s infinite;
}

@keyframes shimmerResult {
    0% { left: -100%; }
    100% { left: 100%; }
}

.dark-theme .result {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.9));
    border: 3px solid transparent;
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    margin-bottom: var(--space-6);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    transition: all 0.4s ease;
}

.dark-theme .result::before {
    content: "RÉSULTAT OFFICIEL";
    display: block;
    font-family: var(--font-family-serif);
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 3px;
    color: #94a3b8;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 2px solid rgba(59, 130, 246, 0.3);
    text-align: center;
}

/* ===== ÉTATS SELON LA MENTION ===== */
.dark-theme .result.result-success {
    border-image: linear-gradient(135deg, #10b981, #34d399) 1;
    background: linear-gradient(135deg, rgba(5, 95, 70, 0.2), rgba(15, 23, 42, 0.9));
    box-shadow: 0 15px 40px rgba(16, 185, 129, 0.3);
}

.dark-theme .result.result-success::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(16, 185, 129, 0.1), transparent);
    pointer-events: none;
}

.dark-theme .result.result-warning {
    border-image: linear-gradient(135deg, #ef4444, #f87171) 1;
    background: linear-gradient(135deg, rgba(153, 27, 27, 0.2), rgba(15, 23, 42, 0.9));
    box-shadow: 0 15px 40px rgba(239, 68, 68, 0.3);
}

.dark-theme .result.result-warning::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(239, 68, 68, 0.1), transparent);
    pointer-events: none;
}

/* ===== ÉLÉMENTS DE RÉSULTAT ===== */
.dark-theme .result-main {
    color: #f1f5f9;
    font-size: 1.6rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: var(--space-4);
    text-align: center;
    position: relative;
    z-index: 2;
}

.dark-theme .result-score {
    font-family: var(--font-family-mono);
    font-size: 2.5rem;
    font-weight: 800;
    text-align: center;
    margin: var(--space-6) 0;
    position: relative;
    z-index: 2;
}

.dark-theme .result-success .result-score {
    color: #6ee7b7;
    text-shadow: 0 0 20px rgba(110, 231, 183, 0.5);
}

.dark-theme .result-warning .result-score {
    color: #fca5a5;
    text-shadow: 0 0 20px rgba(252, 165, 165, 0.5);
}

.dark-theme .result-progress {
    color: #94a3b8;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    margin-top: var(--space-4);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

/* ===== DÉTAILS DES RÉSULTATS ===== */
.dark-theme .result-details {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(51, 65, 85, 0.8));
    border: 2px solid rgba(71, 85, 105, 0.4);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    margin-top: var(--space-6);
    backdrop-filter: blur(15px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.dark-theme .result-details::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #475569, #3b82f6, #475569);
    opacity: 0.6;
}

/* ===== MENTIONS SPÉCIFIQUES ===== */
.dark-theme .mention-tres-bien {
    background: linear-gradient(135deg, rgba(5, 95, 70, 0.3), rgba(15, 23, 42, 0.9));
    border-color: #10b981;
    box-shadow: 0 15px 40px rgba(16, 185, 129, 0.4);
}

.dark-theme .mention-tres-bien .result-score {
    color: #6ee7b7;
    text-shadow: 0 0 25px rgba(110, 231, 183, 0.6);
}

.dark-theme .mention-bien {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.3), rgba(15, 23, 42, 0.9));
    border-color: #3b82f6;
    box-shadow: 0 15px 40px rgba(59, 130, 246, 0.4);
}

.dark-theme .mention-bien .result-score {
    color: #93c5fd;
    text-shadow: 0 0 25px rgba(147, 197, 253, 0.6);
}

.dark-theme .mention-assez-bien {
    background: linear-gradient(135deg, rgba(146, 64, 14, 0.3), rgba(15, 23, 42, 0.9));
    border-color: #f59e0b;
    box-shadow: 0 15px 40px rgba(245, 158, 11, 0.4);
}

.dark-theme .mention-assez-bien .result-score {
    color: #fcd34d;
    text-shadow: 0 0 25px rgba(252, 211, 77, 0.6);
}

.dark-theme .mention-passable {
    background: linear-gradient(135deg, rgba(120, 113, 108, 0.3), rgba(15, 23, 42, 0.9));
    border-color: #78716c;
    box-shadow: 0 15px 40px rgba(120, 113, 108, 0.4);
}

.dark-theme .mention-passable .result-score {
    color: #d6d3d1;
    text-shadow: 0 0 25px rgba(214, 211, 209, 0.6);
}

.dark-theme .mention-ajourne {
    background: linear-gradient(135deg, rgba(153, 27, 27, 0.3), rgba(15, 23, 42, 0.9));
    border-color: #ef4444;
    box-shadow: 0 15px 40px rgba(239, 68, 68, 0.4);
}

.dark-theme .mention-ajourne .result-score {
    color: #fca5a5;
    text-shadow: 0 0 25px rgba(252, 165, 165, 0.6);
}

/* ===== ÉLÉMENTS DE DONNÉES DANS LES RÉSULTATS ===== */
.dark-theme .result-details .data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-3) 0;
    border-bottom: 1px solid rgba(71, 85, 105, 0.3);
    color: #e2e8f0;
}

.dark-theme .result-details .data-row:last-child {
    border-bottom: none;
}

.dark-theme .result-details .data-label {
    font-weight: 600;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.dark-theme .result-details .data-value {
    font-family: var(--font-family-mono);
    font-weight: 700;
    color: #f1f5f9;
    font-size: 1.1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.dark-theme .result-details .mention-badge {
    display: inline-block;
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    position: relative;
    overflow: hidden;
}

.dark-theme .result-details .mention-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.dark-theme .result-details .mention-badge:hover::before {
    left: 100%;
}

/* Couleurs spécifiques pour les badges de mention */
.dark-theme .mention-badge.tres-bien {
    background: linear-gradient(135deg, #10b981, #34d399);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.dark-theme .mention-badge.bien {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.dark-theme .mention-badge.assez-bien {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.dark-theme .mention-badge.passable {
    background: linear-gradient(135deg, #78716c, #a8a29e);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(120, 113, 108, 0.4);
}

.dark-theme .mention-badge.ajourne {
    background: linear-gradient(135deg, #ef4444, #f87171);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

/* ===== ANIMATIONS POUR LES RÉSULTATS ===== */
.dark-theme .result:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
}

.dark-theme .result-success:hover {
    box-shadow: 0 20px 50px rgba(16, 185, 129, 0.4);
}

.dark-theme .result-warning:hover {
    box-shadow: 0 20px 50px rgba(239, 68, 68, 0.4);
}

.dark-theme .toast {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.dark-theme .modal-content {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.98), rgba(30, 41, 59, 0.95));
    border: 1px solid rgba(71, 85, 105, 0.3);
    backdrop-filter: blur(20px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
}

.dark-theme .modal-header {
    border-bottom: 1px solid rgba(71, 85, 105, 0.3);
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.1), transparent);
}

.dark-theme .modal-header h2 {
    color: #60a5fa;
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.dark-theme .modal-close {
    background: rgba(71, 85, 105, 0.3);
    color: #e2e8f0;
    border: 1px solid rgba(71, 85, 105, 0.5);
}

.dark-theme .modal-close:hover {
    background: #ef4444;
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.dark-theme .form-group input,
.dark-theme .form-group select,
.dark-theme .form-group textarea {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.5);
    color: #e2e8f0;
}

.dark-theme .form-group input:focus,
.dark-theme .form-group select:focus,
.dark-theme .form-group textarea:focus {
    background: rgba(30, 64, 175, 0.1);
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.dark-theme .form-group label {
    color: #e2e8f0;
}

.dark-theme .progress-bar {
    background: rgba(71, 85, 105, 0.3);
    border: 1px solid rgba(71, 85, 105, 0.5);
}

.dark-theme .progress-fill {
    background: linear-gradient(90deg, #3b82f6, #10b981);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.dark-theme .filter-btn.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.dark-theme .view-btn.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.dark-theme .controls-panel {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.3);
    backdrop-filter: blur(10px);
}

.dark-theme .validation-controls {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.3);
    backdrop-filter: blur(10px);
}

.dark-theme .semester-selector {
    background: rgba(30, 41, 59, 0.8);
    border: 2px solid rgba(59, 130, 246, 0.3);
    backdrop-filter: blur(10px);
}

.dark-theme .semester-selector h3 {
    color: #60a5fa;
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.dark-theme .app-header {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.3);
    backdrop-filter: blur(10px);
}

/* ===== CELLULES SPÉCIALISÉES DU TABLEAU ===== */
.dark-theme .notes-table .module-name {
    color: #f1f5f9;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.6), rgba(51, 65, 85, 0.4));
    border-left: 3px solid rgba(59, 130, 246, 0.5);
    position: relative;
}

.dark-theme .notes-table .module-name::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, #3b82f6, #10b981);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dark-theme .notes-table tbody tr:hover .module-name::before {
    opacity: 1;
}

.dark-theme .notes-table .coef {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.3), rgba(59, 130, 246, 0.2));
    color: #93c5fd;
    font-weight: 700;
    text-align: center;
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--radius-sm);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.dark-theme .notes-table .coef::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(147, 197, 253, 0.2), transparent);
    transition: left 0.5s;
}

.dark-theme .notes-table tbody tr:hover .coef::before {
    left: 100%;
}

/* ===== INPUTS DE NOTES AMÉLIORÉS ===== */
.dark-theme .notes-table .grade-input input {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.6));
    border: 2px solid rgba(71, 85, 105, 0.4);
    color: #f1f5f9;
    font-weight: 600;
    text-align: center;
    border-radius: var(--radius);
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark-theme .notes-table .grade-input input:hover {
    border-color: rgba(59, 130, 246, 0.6);
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.1), rgba(15, 23, 42, 0.8));
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.dark-theme .notes-table .grade-input input:focus {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.15), rgba(15, 23, 42, 0.9));
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    color: #ffffff;
    transform: scale(1.05);
}

/* ===== ÉTATS DE VALIDATION AMÉLIORÉS ===== */
.dark-theme .notes-table .grade-input input.error {
    background: linear-gradient(135deg, rgba(153, 27, 27, 0.2), rgba(15, 23, 42, 0.8));
    border-color: #ef4444;
    color: #fca5a5;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.dark-theme .notes-table .grade-input input.error:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
}

.dark-theme .notes-table .grade-input input.warning {
    background: linear-gradient(135deg, rgba(146, 64, 14, 0.2), rgba(15, 23, 42, 0.8));
    border-color: #f59e0b;
    color: #fcd34d;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.dark-theme .notes-table .grade-input input.warning:focus {
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.3);
}

.dark-theme .notes-table .grade-input input.success {
    background: linear-gradient(135deg, rgba(5, 95, 70, 0.2), rgba(15, 23, 42, 0.8));
    border-color: #10b981;
    color: #6ee7b7;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.dark-theme .notes-table .grade-input input.success:focus {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
}

/* ===== MOYENNES DE MODULES SOPHISTIQUÉES ===== */
.dark-theme .module-average {
    font-family: var(--font-family-mono);
    font-weight: 700;
    text-align: center;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius);
    font-size: 0.95rem;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    backdrop-filter: blur(5px);
}

/* États selon la performance du module */
.dark-theme .module-average.excellent {
    background: linear-gradient(135deg, rgba(5, 95, 70, 0.5), rgba(16, 185, 129, 0.3));
    border-color: #10b981;
    color: #6ee7b7;
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.4);
}

.dark-theme .module-average.tres-bien {
    background: linear-gradient(135deg, rgba(5, 95, 70, 0.4), rgba(16, 185, 129, 0.25));
    border-color: #34d399;
    color: #6ee7b7;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.dark-theme .module-average.bien {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.4), rgba(59, 130, 246, 0.25));
    border-color: #3b82f6;
    color: #93c5fd;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.dark-theme .module-average.assez-bien {
    background: linear-gradient(135deg, rgba(146, 64, 14, 0.4), rgba(245, 158, 11, 0.25));
    border-color: #f59e0b;
    color: #fcd34d;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.dark-theme .module-average.passable {
    background: linear-gradient(135deg, rgba(120, 113, 108, 0.4), rgba(168, 162, 158, 0.25));
    border-color: #78716c;
    color: #d6d3d1;
    box-shadow: 0 4px 15px rgba(120, 113, 108, 0.3);
}

.dark-theme .module-average.insuffisant {
    background: linear-gradient(135deg, rgba(153, 27, 27, 0.4), rgba(239, 68, 68, 0.25));
    border-color: #ef4444;
    color: #fca5a5;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.dark-theme .module-average.non-note {
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.4), rgba(71, 85, 105, 0.25));
    border-color: #475569;
    color: #94a3b8;
    box-shadow: 0 4px 15px rgba(71, 85, 105, 0.2);
}

/* Animations de brillance pour les moyennes */
.dark-theme .module-average::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    transition: left 0.8s;
}

.dark-theme .module-average:hover::before {
    left: 100%;
}

.dark-theme .module-average:hover {
    transform: translateY(-1px) scale(1.02);
}

/* ===== CELLULE DE MOYENNE GÉNÉRALE DANS LE TABLEAU ===== */
.dark-theme .notes-table .general-average-cell {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.8));
    border: 3px solid transparent;
    border-radius: var(--radius);
    padding: var(--space-4);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.dark-theme .notes-table .general-average-cell.excellent {
    border-image: linear-gradient(135deg, #10b981, #34d399) 1;
    background: linear-gradient(135deg, rgba(5, 95, 70, 0.3), rgba(15, 23, 42, 0.9));
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.dark-theme .notes-table .general-average-cell.tres-bien {
    border-image: linear-gradient(135deg, #10b981, #34d399) 1;
    background: linear-gradient(135deg, rgba(5, 95, 70, 0.25), rgba(15, 23, 42, 0.9));
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.dark-theme .notes-table .general-average-cell.bien {
    border-image: linear-gradient(135deg, #3b82f6, #60a5fa) 1;
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.25), rgba(15, 23, 42, 0.9));
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.dark-theme .notes-table .general-average-cell.assez-bien {
    border-image: linear-gradient(135deg, #f59e0b, #fbbf24) 1;
    background: linear-gradient(135deg, rgba(146, 64, 14, 0.25), rgba(15, 23, 42, 0.9));
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
}

.dark-theme .notes-table .general-average-cell.passable {
    border-image: linear-gradient(135deg, #78716c, #a8a29e) 1;
    background: linear-gradient(135deg, rgba(120, 113, 108, 0.25), rgba(15, 23, 42, 0.9));
    box-shadow: 0 8px 20px rgba(120, 113, 108, 0.3);
}

.dark-theme .notes-table .general-average-cell.insuffisant {
    border-image: linear-gradient(135deg, #ef4444, #f87171) 1;
    background: linear-gradient(135deg, rgba(153, 27, 27, 0.25), rgba(15, 23, 42, 0.9));
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
}

/* ===== VALEURS DE MOYENNES AVEC COULEURS SPÉCIFIQUES ===== */
.dark-theme .notes-table .general-average-value.excellent,
.dark-theme .notes-table .general-average-value.tres-bien {
    color: #6ee7b7;
    text-shadow: 0 0 15px rgba(110, 231, 183, 0.6);
    font-weight: 800;
}

.dark-theme .notes-table .general-average-value.bien {
    color: #93c5fd;
    text-shadow: 0 0 15px rgba(147, 197, 253, 0.6);
    font-weight: 800;
}

.dark-theme .notes-table .general-average-value.assez-bien {
    color: #fcd34d;
    text-shadow: 0 0 15px rgba(252, 211, 77, 0.6);
    font-weight: 800;
}

.dark-theme .notes-table .general-average-value.passable {
    color: #d6d3d1;
    text-shadow: 0 0 15px rgba(214, 211, 209, 0.6);
    font-weight: 800;
}

.dark-theme .notes-table .general-average-value.insuffisant {
    color: #fca5a5;
    text-shadow: 0 0 15px rgba(252, 165, 165, 0.6);
    font-weight: 800;
}

/* ===== MENTION DANS LE TABLEAU ===== */
.dark-theme .notes-table .mention-cell {
    font-family: var(--font-family-serif);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-align: center;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius);
    position: relative;
    overflow: hidden;
}

.dark-theme .notes-table .mention-cell.excellent,
.dark-theme .notes-table .mention-cell.tres-bien {
    background: linear-gradient(135deg, #10b981, #34d399);
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.dark-theme .notes-table .mention-cell.bien {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.dark-theme .notes-table .mention-cell.assez-bien {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.dark-theme .notes-table .mention-cell.passable {
    background: linear-gradient(135deg, #78716c, #a8a29e);
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    box-shadow: 0 4px 15px rgba(120, 113, 108, 0.4);
}

.dark-theme .notes-table .mention-cell.insuffisant {
    background: linear-gradient(135deg, #ef4444, #f87171);
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

/* Animation de brillance pour les mentions */
.dark-theme .notes-table .mention-cell::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.dark-theme .notes-table .mention-cell:hover::before {
    left: 100%;
}

/* ===== EFFETS DE SÉLECTION ET FOCUS ===== */
.dark-theme .notes-table tbody tr.selected {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.3), rgba(59, 130, 246, 0.2));
    border: 2px solid rgba(59, 130, 246, 0.6);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.dark-theme .notes-table tbody tr:focus-within {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.15), rgba(59, 130, 246, 0.1));
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4);
}

.dark-theme .nav-mobile-menu {
    background: rgba(15, 23, 42, 0.98);
    border-top: 1px solid rgba(71, 85, 105, 0.3);
    backdrop-filter: blur(20px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
}

.dark-theme .nav-mobile-link {
    background: rgba(30, 41, 59, 0.6);
    border: 1px solid rgba(71, 85, 105, 0.3);
    color: #e2e8f0;
}

.dark-theme .nav-mobile-link:hover {
    background: rgba(30, 64, 175, 0.2);
    border-color: #3b82f6;
    color: #60a5fa;
}

.dark-theme .nav-mobile-link.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-color: #60a5fa;
    color: #ffffff;
}

.dark-theme .nav-mobile-btn {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.3);
    color: #e2e8f0;
}

.dark-theme .nav-mobile-btn:hover {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-color: #60a5fa;
    color: #ffffff;
}

/* ===== RESET ET BASE ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    background: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    transition: var(--transition);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== EN-TÊTE OFFICIEL ===== */
.official-header {
    background: var(--bg-primary);
    border-bottom: 3px solid var(--primary);
    padding: var(--space-6) var(--space-4);
    text-align: center;
    box-shadow: var(--shadow-md);
    margin-bottom: var(--space-8);
}

.republic-title {
    font-family: var(--font-family-serif);
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: var(--space-2);
    line-height: 1.4;
}

.ministry-title {
    font-family: var(--font-family-serif);
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--space-4);
}

.university-info {
    font-family: var(--font-family-serif);
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: var(--space-6);
    padding: var(--space-3) var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius);
    border: 1px solid var(--border-secondary);
}

.document-title {
    font-family: var(--font-family-serif);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: var(--space-4);
    padding: var(--space-4);
    background: linear-gradient(135deg, var(--bg-accent), var(--bg-primary));
    border: 2px solid var(--primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow);
}

/* ===== NAVBAR MODERNE ET PROFESSIONNELLE ===== */
.navbar {
    background: var(--bg-primary);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-secondary);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--accent), var(--primary));
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

/* ===== LOGO ET MARQUE ===== */
.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    text-decoration: none;
    position: relative;
}

.nav-logo {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.nav-logo:hover {
    transform: scale(1.05) rotate(5deg);
    box-shadow: var(--shadow-lg);
}

.nav-icon {
    font-size: 1.8rem;
    color: var(--white);
    z-index: 2;
}

.logo-animation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: var(--radius-lg);
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.nav-logo:hover .logo-animation {
    transform: translateX(100%);
}

.nav-brand-text {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.nav-title {
    font-family: var(--font-family-serif);
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--primary);
    line-height: 1;
    letter-spacing: 0.5px;
}

.nav-subtitle {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* ===== MENU DE NAVIGATION CENTRAL ===== */
.nav-menu {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: center;
}

.nav-links {
    display: flex;
    gap: var(--space-2);
    background: var(--bg-secondary);
    padding: var(--space-1);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-secondary);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius);
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.1), transparent);
    transition: left 0.5s;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    color: var(--primary);
    background: var(--bg-accent);
    transform: translateY(-1px);
}

.nav-link.active {
    background: var(--primary);
    color: var(--white);
    box-shadow: var(--shadow);
}

.nav-link.active:hover {
    background: var(--primary-dark);
}

.nav-link-icon {
    font-size: 1.1rem;
}

.nav-link-text {
    font-weight: 600;
    white-space: nowrap;
}

/* ===== ACTIONS DE LA NAVBAR ===== */
.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.nav-tools {
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

.nav-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.nav-btn:hover::before {
    left: 100%;
}

.nav-btn:hover {
    background: var(--primary);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary);
}

.nav-btn-icon {
    width: 42px;
    height: 42px;
    font-size: 1.2rem;
    position: relative;
}

.btn-tooltip {
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: var(--white);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.7rem;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: var(--transition);
    z-index: 1000;
}

.nav-btn-icon:hover .btn-tooltip {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
}

.nav-divider {
    width: 1px;
    height: 30px;
    background: var(--border-primary);
    margin: 0 var(--space-2);
}

.nav-settings {
    display: flex;
    align-items: center;
}

.nav-btn-theme {
    padding: var(--space-2) var(--space-4);
    gap: var(--space-2);
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: 0.9rem;
}

.theme-icon {
    font-size: 1.1rem;
    transition: var(--transition);
}

.nav-btn-theme:hover .theme-icon {
    transform: rotate(180deg);
}

.theme-text {
    font-weight: 600;
    white-space: nowrap;
}

/* ===== MENU MOBILE ===== */
.nav-mobile-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    gap: 4px;
}

.hamburger-line {
    width: 20px;
    height: 2px;
    background: var(--text-secondary);
    transition: var(--transition);
    border-radius: 1px;
}

.nav-mobile-toggle:hover {
    background: var(--primary);
}

.nav-mobile-toggle:hover .hamburger-line {
    background: var(--white);
}

.nav-mobile-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.nav-mobile-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.nav-mobile-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.nav-mobile-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-secondary);
    box-shadow: var(--shadow-lg);
    padding: var(--space-4);
    animation: slideDown 0.3s ease-out;
}

.nav-mobile-menu.active {
    display: block;
}

.nav-mobile-links {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
}

.nav-mobile-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius);
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: var(--transition);
    border: 1px solid var(--border-secondary);
}

.nav-mobile-link:hover {
    background: var(--bg-accent);
    color: var(--primary);
    border-color: var(--primary);
}

.nav-mobile-link.active {
    background: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.nav-mobile-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-2);
    border-top: 1px solid var(--border-secondary);
    padding-top: var(--space-4);
}

.nav-mobile-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3);
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.nav-mobile-btn:hover {
    background: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

/* ===== ANIMATIONS NAVBAR ===== */
@keyframes navSlideIn {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes logoSpin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.navbar {
    animation: navSlideIn 0.5s ease-out;
}

.nav-logo.loading {
    animation: logoSpin 1s linear infinite;
}

/* Effet de survol pour les liens de navigation */
.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--accent);
    transition: var(--transition);
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 80%;
}

.nav-link.active::after {
    width: 100%;
    background: var(--white);
}

/* Indicateur de notification */
.nav-btn-icon::after {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: var(--error);
    border-radius: 50%;
    opacity: 0;
    transform: scale(0);
    transition: var(--transition);
}

.nav-btn-icon.has-notification::after {
    opacity: 1;
    transform: scale(1);
}

/* Amélioration du menu mobile */
.nav-mobile-menu {
    backdrop-filter: blur(10px);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.nav-mobile-link {
    position: relative;
    overflow: hidden;
}

.nav-mobile-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.1), transparent);
    transition: left 0.5s;
}

.nav-mobile-link:hover::before {
    left: 100%;
}

/* Badge de progression */
.nav-progress-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--accent);
    color: var(--white);
    font-size: 0.7rem;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    min-width: 18px;
    text-align: center;
    box-shadow: var(--shadow);
}

/* Effet de focus amélioré */
.nav-btn:focus-visible,
.nav-link:focus-visible,
.nav-mobile-toggle:focus-visible {
    outline: 2px solid var(--accent);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(5, 150, 105, 0.2);
}

/* ===== CONTENEUR PRINCIPAL ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    background: var(--bg-primary);
    padding: var(--space-8);
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-secondary);
    position: relative;
}

/* ===== TYPOGRAPHIE OFFICIELLE ===== */
h1 {
    font-family: var(--font-family-serif);
    text-align: center;
    margin: var(--space-6) 0 var(--space-8) 0;
    color: var(--primary);
    font-size: 2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    line-height: 1.2;
    position: relative;
}

h1::after {
    content: '';
    position: absolute;
    bottom: -var(--space-3);
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: var(--radius-sm);
}

h2 {
    font-family: var(--font-family-serif);
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--space-4);
    text-transform: uppercase;
    letter-spacing: 1px;
}

h3 {
    font-family: var(--font-family-primary);
    color: var(--text-secondary);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--space-3);
}

p {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: var(--space-4);
    text-align: left;
}

/* ===== TABLEAU OFFICIEL DE NOTES ===== */
.notes-table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--space-8) 0;
    font-family: var(--font-family-primary);
    font-size: 0.9rem;
    background: var(--bg-primary);
    border: 2px solid var(--primary);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
}

.modules-grid {
    transition: var(--transition);
}

/* Gestion des vues */
.modules-grid:not(.modules-cards) .module-card-view {
    display: none;
}

.modules-cards .notes-table {
    display: none;
}

/* En-têtes de tableau */
.notes-table thead {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: var(--white);
}

.notes-table th {
    padding: var(--space-4) var(--space-3);
    text-align: center;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

.notes-table th:last-child {
    border-right: none;
}

/* Corps du tableau */
.notes-table tbody tr {
    transition: var(--transition);
    border-bottom: 1px solid var(--border-secondary);
}

.notes-table tbody tr:nth-child(even) {
    background: var(--bg-secondary);
}

.notes-table tbody tr:hover {
    background: var(--bg-accent);
    transform: scale(1.01);
    box-shadow: var(--shadow);
}

.notes-table td {
    padding: var(--space-3) var(--space-2);
    text-align: center;
    vertical-align: middle;
    border-right: 1px solid var(--border-secondary);
    font-weight: 500;
}

.notes-table td:last-child {
    border-right: none;
}

/* Nom du module */
.notes-table .module-name {
    text-align: left;
    font-weight: 600;
    padding-left: var(--space-4);
    color: var(--text-primary);
    max-width: 250px;
    font-size: 0.9rem;
}

/* Coefficient */
.notes-table .coef {
    font-weight: 700;
    background: var(--bg-accent);
    color: var(--primary);
    font-family: var(--font-family-mono);
}

/* Inputs de notes */
.notes-table .grade-input {
    position: relative;
}

.notes-table .grade-input input {
    width: 70px;
    padding: var(--space-2);
    border: 2px solid var(--border-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    text-align: center;
    font-size: 0.9rem;
    font-weight: 600;
    font-family: var(--font-family-mono);
    border-radius: var(--radius);
    transition: var(--transition);
}

.notes-table .grade-input input:focus {
    outline: none;
    border-color: var(--primary);
    background: var(--bg-accent);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
    transform: scale(1.05);
}

/* États de validation des inputs */
.notes-table .grade-input input.error {
    border-color: var(--error);
    background: var(--bg-error);
    color: var(--error);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.notes-table .grade-input input.warning {
    border-color: var(--warning);
    background: var(--bg-warning);
    color: var(--warning);
    box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.1);
}

.notes-table .grade-input input:valid,
.notes-table .grade-input input.success {
    border-color: var(--success);
    background: var(--bg-success);
    color: var(--success);
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

/* Messages de validation professionnels */
.validation-message {
    position: absolute;
    z-index: 1000;
    background: var(--bg-primary);
    border: 2px solid;
    border-radius: var(--radius);
    padding: var(--space-2) var(--space-3);
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: var(--space-1);
    box-shadow: var(--shadow-lg);
    max-width: 220px;
    word-wrap: break-word;
    font-family: var(--font-family-primary);
}

.validation-message.error {
    border-color: var(--error);
    background: var(--bg-error);
    color: var(--error);
}

.validation-message.warning {
    border-color: var(--warning);
    background: var(--bg-warning);
    color: var(--warning);
}

/* Position relative pour les cellules d'input */
.notes-table .grade-input {
    position: relative;
}

/* ===== MOYENNE DU MODULE ===== */
.module-average {
    font-weight: 700;
    color: var(--accent);
    background: var(--bg-success);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius);
    font-family: var(--font-family-mono);
    border: 2px solid var(--accent);
    margin-top: var(--space-4);
    text-align: center;
    font-size: 1.1rem;
    box-shadow: var(--shadow);
}

/* ===== BOUTONS PROFESSIONNELS ===== */
.controls {
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    margin: var(--space-8) 0;
    flex-wrap: wrap;
    padding: var(--space-6);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-secondary);
}

.btn {
    background: var(--primary);
    color: var(--white);
    border: none;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    font-family: var(--font-family-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    min-width: 120px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow);
}

.btn-secondary {
    background: var(--secondary);
    color: var(--white);
}

.btn-secondary:hover {
    background: var(--text-secondary);
    border-color: var(--secondary);
}

.btn-success {
    background: var(--success);
    color: var(--white);
}

.btn-success:hover {
    background: var(--accent-light);
    border-color: var(--success);
}

.btn-danger {
    background: var(--error);
    color: var(--white);
}

.btn-danger:hover {
    background: #ef4444;
    border-color: var(--error);
}

.btn-warning {
    background: var(--warning);
    color: var(--white);
}

.btn-warning:hover {
    background: #f59e0b;
    border-color: var(--warning);
}

.btn-info {
    background: var(--info);
    color: var(--white);
}

.btn-info:hover {
    background: var(--primary-light);
    border-color: var(--info);
}

/* Résumé de validation */
.validation-summary {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffc107;
    border-radius: 8px;
    margin: 20px 0;
    padding: 0;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
    animation: slideDown 0.3s ease-out;
}

.summary-content {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    gap: 12px;
}

.summary-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.summary-text {
    flex: 1;
    font-weight: 500;
    color: #856404;
    font-size: 14px;
}

.summary-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #856404;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.summary-close:hover {
    background-color: rgba(133, 100, 4, 0.1);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== SÉLECTEUR DE SEMESTRE PROFESSIONNEL ===== */
.semester-selector {
    text-align: center;
    margin: var(--space-8) 0;
    padding: var(--space-6);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 2px solid var(--primary);
    box-shadow: var(--shadow-lg);
}

.semester-selector h3 {
    margin-bottom: var(--space-4);
    color: var(--primary);
    font-size: 1.3rem;
    font-weight: 700;
    font-family: var(--font-family-serif);
    text-transform: uppercase;
    letter-spacing: 2px;
}

.semester-buttons {
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

.semester-btn {
    padding: var(--space-4) var(--space-6);
    border: 2px solid var(--primary);
    background: var(--bg-primary);
    color: var(--primary);
    font-size: 1rem;
    font-weight: 600;
    font-family: var(--font-family-primary);
    cursor: pointer;
    border-radius: var(--radius);
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 180px;
    position: relative;
    overflow: hidden;
}

.semester-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.1), transparent);
    transition: left 0.5s;
}

.semester-btn:hover::before {
    left: 100%;
}

.semester-btn:hover {
    background: var(--primary);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.semester-btn.active {
    background: var(--primary);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.semester-btn.active:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* Boutons de contrôle de validation */
.validation-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 25px 0;
    flex-wrap: wrap;
}

.validate-btn,
.clear-btn {
    padding: 12px 24px;
    border: 2px solid;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Times New Roman', serif;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.validate-btn {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.validate-btn:hover {
    background: white;
    color: #28a745;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.clear-btn {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.clear-btn:hover {
    background: white;
    color: #dc3545;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.notes-table .not-applicable {
    color: #999;
    font-style: italic;
}

.notes-table .module-avg,
.notes-table .weighted-avg {
    font-weight: bold;
    background-color: #f9f9f9;
}

.notes-table tbody tr:nth-child(even) {
    background-color: #fafafa;
}

.notes-table tbody tr:hover {
    background-color: #f0f8ff;
}

/* Styles pour les lignes de total */
.notes-table tfoot {
    border-top: 3px solid #000;
}

.notes-table .total-row,
.notes-table .coef-total-row {
    background-color: #e8f4f8;
    font-weight: bold;
}

.notes-table .total-label {
    text-align: right;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding-right: 15px;
}

.notes-table .general-average {
    background-color: #d4edda;
    color: #155724;
    font-size: 16px;
    font-weight: bold;
}

.notes-table .total-weighted {
    background-color: #f8f9fa;
    font-weight: bold;
}

.notes-table .total-coef {
    background-color: #fff3cd;
    color: #856404;
    font-weight: bold;
}

.notes-table .mention {
    background-color: #d1ecf1;
    color: #0c5460;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}



/* ===== STATISTIQUES ET EN-TÊTE ===== */
.app-header {
    text-align: center;
    margin-bottom: var(--space-8);
    padding: var(--space-6);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-secondary);
    box-shadow: var(--shadow);
}

.stats-bar {
    display: flex;
    justify-content: center;
    gap: var(--space-6);
    margin-top: var(--space-4);
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: var(--space-3) var(--space-4);
    background: var(--bg-accent);
    border-radius: var(--radius);
    border: 1px solid var(--border-accent);
    min-width: 100px;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--space-1);
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    font-family: var(--font-family-mono);
}

/* ===== RÉSULTAT OFFICIEL ===== */
.result-container {
    margin: var(--space-8) 0;
    text-align: center;
}

.result {
    background: var(--bg-primary);
    border: 3px solid var(--primary);
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    margin: 0 auto var(--space-6);
    max-width: 600px;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.result::before {
    content: "RÉSULTAT OFFICIEL";
    display: block;
    font-family: var(--font-family-serif);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: var(--text-muted);
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-2);
    border-bottom: 2px solid var(--border-primary);
}

.result-details {
    background: var(--bg-secondary);
    border-radius: var(--radius);
    padding: var(--space-4);
    border: 1px solid var(--border-secondary);
    max-width: 600px;
    margin: 0 auto;
    font-family: var(--font-family-primary);
    color: var(--text-secondary);
}



/* ===== RESPONSIVE DESIGN PROFESSIONNEL ===== */
@media (max-width: 768px) {
    body {
        padding: var(--space-2);
    }

    .official-header {
        padding: var(--space-4) var(--space-2);
    }

    .republic-title {
        font-size: 0.9rem;
    }

    .ministry-title {
        font-size: 0.8rem;
    }

    .document-title {
        font-size: 1.2rem;
        padding: var(--space-3);
    }

    .container {
        padding: var(--space-4);
    }

    .nav-container {
        padding: 0 var(--space-4);
        height: 60px;
    }

    .nav-brand-text {
        display: none;
    }

    .nav-logo {
        width: 45px;
        height: 45px;
    }

    .nav-menu {
        display: none;
    }

    .nav-tools {
        display: none;
    }

    .nav-divider {
        display: none;
    }

    .nav-btn-theme .theme-text {
        display: none;
    }

    .nav-btn-theme {
        padding: var(--space-2);
        width: 42px;
        height: 42px;
    }

    .nav-mobile-toggle {
        display: flex;
    }

    h1 {
        font-size: 1.5rem;
        margin: var(--space-4) 0 var(--space-6) 0;
    }

    .stats-bar {
        gap: var(--space-3);
    }

    .stat-item {
        min-width: 80px;
        padding: var(--space-2) var(--space-3);
    }

    .stat-value {
        font-size: 1.2rem;
    }

    .notes-table {
        font-size: 0.8rem;
    }

    .notes-table th,
    .notes-table td {
        padding: var(--space-2) var(--space-1);
    }

    .notes-table .module-name {
        font-size: 0.75rem;
        padding-left: var(--space-2);
    }

    .notes-table .grade-input input {
        width: 50px;
        font-size: 0.8rem;
        padding: var(--space-1);
    }

    .controls {
        gap: var(--space-2);
        padding: var(--space-4);
    }

    .btn {
        padding: var(--space-2) var(--space-4);
        font-size: 0.8rem;
        min-width: 100px;
    }

    .result {
        font-size: 1.4rem;
        padding: var(--space-6);
        margin: var(--space-4) auto;
    }

    .semester-selector {
        padding: var(--space-4);
        margin: var(--space-6) 0;
    }

    .semester-selector h3 {
        font-size: 1.1rem;
        margin-bottom: var(--space-3);
    }

    .semester-buttons {
        gap: var(--space-3);
    }

    .semester-btn {
        padding: var(--space-3) var(--space-4);
        font-size: 0.9rem;
        min-width: 150px;
    }
}

@media (max-width: 480px) {
    .official-header {
        padding: var(--space-3) var(--space-1);
    }

    .republic-title {
        font-size: 0.8rem;
        line-height: 1.3;
    }

    .ministry-title {
        font-size: 0.7rem;
    }

    .document-title {
        font-size: 1rem;
        padding: var(--space-2);
        letter-spacing: 1px;
    }

    .container {
        padding: var(--space-2);
    }

    .nav-brand {
        font-size: 0.9rem;
    }

    .nav-actions {
        gap: var(--space-1);
    }

    .nav-btn {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    h1 {
        font-size: 1.3rem;
    }

    .stats-bar {
        flex-direction: column;
        gap: var(--space-2);
    }

    .stat-item {
        min-width: auto;
        width: 100%;
    }

    .notes-table {
        font-size: 0.7rem;
    }

    .notes-table th,
    .notes-table td {
        padding: var(--space-1);
    }

    .notes-table .grade-input input {
        width: 40px;
        font-size: 0.7rem;
    }

    .controls {
        flex-direction: column;
        gap: var(--space-2);
    }

    .btn {
        width: 100%;
        padding: var(--space-3);
        font-size: 0.8rem;
    }

    .result {
        font-size: 1.2rem;
        padding: var(--space-4);
    }

    .semester-selector {
        padding: var(--space-3);
        margin: var(--space-4) 0;
    }

    .semester-selector h3 {
        font-size: 1rem;
        margin-bottom: var(--space-2);
    }

    .semester-buttons {
        flex-direction: column;
        gap: var(--space-2);
        align-items: center;
    }

    .semester-btn {
        padding: var(--space-3) var(--space-4);
        font-size: 0.8rem;
        width: 100%;
        max-width: 250px;
        min-width: auto;
    }
}

/* ===== ANIMATIONS ET TRANSITIONS ===== */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Animation pour les éléments qui apparaissent */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-down {
    animation: slideDown 0.3s ease-out;
}

/* Animation pour les boutons actifs */
.btn.pulse {
    animation: pulse 2s infinite;
}

/* Amélioration de l'accessibilité */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus visible pour l'accessibilité */
button:focus-visible,
input:focus-visible,
select:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Amélioration du contraste pour l'accessibilité */
@media (prefers-contrast: high) {
    :root {
        --border-primary: #000000;
        --border-secondary: #333333;
        --text-primary: #000000;
        --text-secondary: #333333;
    }
}